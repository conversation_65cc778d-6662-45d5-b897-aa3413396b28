"""
音频播放器组件模块 - 提供音频播放与波形显示功能
"""

import os
import numpy as np
import tempfile
from PySide6.QtCore import Qt, Signal, QTimer, QSize, QPointF, Property
from PySide6.QtWidgets import (
    QWidget, QFrame, QVBoxLayout, QHBoxLayout, 
    QLabel, QPushButton, QSlider, QSizePolicy
)
from PySide6.QtGui import QPainter, QPen, QColor, QLinearGradient, QPainterPath

from src.utils.style import COLORS
from src.components.icons import SVG_ICONS, create_svg_pixmap

# 检查pydub是否可用
try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    print("警告: pydub未安装，音频波形分析功能将不可用。请安装pydub: pip install pydub")


class WaveformDisplay(QWidget):
    """波形显示组件"""
    
    positionChanged = Signal(float)  # 发出位置变化信号，参数为0-1之间的比例
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置最小尺寸
        self.setMinimumHeight(60)
        self.setMinimumWidth(200)
        
        # 设置尺寸策略
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # 波形数据，初始为空
        self.waveform_data = []
        
        # 播放位置比例 (0-1)
        self.position_ratio = 0.0
        
        # 设置接受鼠标事件
        self.setMouseTracking(True)
        
        # 颜色设置
        self.played_color = QColor(COLORS['accent_blue'])
        self.unplayed_color = QColor(COLORS['text_secondary'])
        
    def set_waveform_data(self, data):
        """设置波形数据"""
        self.waveform_data = data
        self.update()  # 重绘组件
        
    def set_position(self, ratio):
        """设置播放位置比例"""
        self.position_ratio = max(0.0, min(1.0, ratio))  # 确保在0-1范围内
        self.update()  # 重绘组件
        
    def mousePressEvent(self, event):
        """处理鼠标按下事件，用于更改播放位置"""
        if event.button() == Qt.LeftButton:
            width = self.width()
            if width > 0:
                ratio = max(0.0, min(1.0, event.x() / width))
                self.position_ratio = ratio
                self.positionChanged.emit(ratio)
                self.update()  # 重绘组件
                
    def paintEvent(self, event):
        """绘制波形"""
        if not self.waveform_data:
            return
            
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 获取绘图区域
        width = self.width()
        height = self.height()
        
        # 计算分界点
        split_x = int(width * self.position_ratio)
        
        # 确定每个采样点占用的宽度
        samples = len(self.waveform_data)
        if samples < 1:
            return
            
        # 重新采样波形以适应宽度
        if width < samples:
            # 下采样
            resample_factor = samples / width
            resampled_data = []
            for i in range(0, width):
                start_idx = int(i * resample_factor)
                end_idx = int((i + 1) * resample_factor)
                if start_idx >= samples:
                    start_idx = samples - 1
                if end_idx >= samples:
                    end_idx = samples - 1
                segment = self.waveform_data[start_idx:end_idx+1]
                if len(segment) > 0:
                    resampled_data.append(np.max(segment))
                else:
                    resampled_data.append(0)
            waveform = resampled_data
        else:
            # 插值
            step = width / samples
            waveform = []
            for i in range(samples):
                waveform.append(self.waveform_data[i])
                if i < samples - 1 and step > 1:
                    # 添加插值点
                    for j in range(1, int(step)):
                        t = j / step
                        interpolated = (1 - t) * self.waveform_data[i] + t * self.waveform_data[i+1]
                        waveform.append(interpolated)
            while len(waveform) < width:
                waveform.append(waveform[-1] if waveform else 0)
                
        # 绘制波形
        for i in range(min(len(waveform), width)):
            # 选择颜色
            if i < split_x:
                painter.setPen(QPen(self.played_color, 1))
            else:
                painter.setPen(QPen(self.unplayed_color, 1))
                
            # 计算高度，最大值为高度的一半
            amp = waveform[i] * (height / 2.5)
            amp = min(amp, height / 2)
            
            # 计算中心线位置
            center_y = height / 2
            
            # 绘制线条
            painter.drawLine(
                i, int(center_y - amp),
                i, int(center_y + amp)
            )


class AudioPlayer(QFrame):
    """音频播放器组件"""
    
    # 状态信号
    playbackStarted = Signal()
    playbackPaused = Signal()
    playbackStopped = Signal()
    playbackPositionChanged = Signal(int)  # 毫秒
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置基本属性
        self.setObjectName("audioPlayer")
        self.setStyleSheet(f"background-color: {COLORS['bg_dark_secondary']}; border-radius: 8px;")
        
        # 设置尺寸策略
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.setMinimumHeight(180)
        
        # 音频相关属性
        self.audio_file = None
        self.audio_data = None
        self.audio_length_ms = 0
        self.current_position_ms = 0
        self.is_playing = False
        self.waveform_data = []
        
        # 播放计时器
        self.playback_timer = QTimer(self)
        self.playback_timer.setInterval(50)  # 每50毫秒更新一次
        self.playback_timer.timeout.connect(self._update_playback_position)
        
        # 设置UI
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(12)
        
        # 音频信息区域
        info_layout = QHBoxLayout()
        
        # 音频文件名标签
        self.file_name_label = QLabel("未加载音频")
        self.file_name_label.setStyleSheet(f"""
            color: {COLORS['text_primary']};
            font-size: 14px;
            font-weight: 600;
        """)
        info_layout.addWidget(self.file_name_label)
        
        # 音频时长标签
        self.duration_label = QLabel("00:00")
        self.duration_label.setStyleSheet(f"""
            color: {COLORS['text_secondary']};
            font-size: 14px;
        """)
        self.duration_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        info_layout.addWidget(self.duration_label)
        
        main_layout.addLayout(info_layout)
        
        # 波形显示
        self.waveform_display = WaveformDisplay()
        self.waveform_display.positionChanged.connect(self.seek_to_position)
        main_layout.addWidget(self.waveform_display)
        
        # 播放控制区域
        controls_layout = QHBoxLayout()
        controls_layout.setContentsMargins(0, 8, 0, 0)
        
        # 播放/暂停按钮
        self.play_button = QPushButton()
        self.play_button.setProperty("buttonType", "round")
        self.play_button.setFixedSize(QSize(36, 36))
        self.play_button.setCursor(Qt.PointingHandCursor)
        play_pixmap = create_svg_pixmap(SVG_ICONS["play"], 20, COLORS["text_primary"])
        self.play_button.setIcon(play_pixmap)
        self.play_button.setIconSize(QSize(20, 20))
        self.play_button.clicked.connect(self.toggle_playback)
        controls_layout.addWidget(self.play_button)
        
        # 时间位置标签
        self.position_label = QLabel("00:00")
        self.position_label.setStyleSheet(f"""
            color: {COLORS['text_primary']};
            font-size: 14px;
            margin-left: 8px;
        """)
        controls_layout.addWidget(self.position_label)
        
        # 添加伸缩项
        controls_layout.addStretch()
        
        main_layout.addLayout(controls_layout)
        
    def load_audio_file(self, file_path):
        """加载音频文件并分析波形"""
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在 - {file_path}")
            return False
            
        try:
            self.audio_file = file_path
            file_name = os.path.basename(file_path)
            self.file_name_label.setText(file_name)
            
            if PYDUB_AVAILABLE:
                # 使用pydub加载音频并分析波形
                audio = AudioSegment.from_file(file_path)
                self.audio_length_ms = len(audio)
                
                # 设置时长标签
                duration_str = self._format_time(self.audio_length_ms)
                self.duration_label.setText(duration_str)
                
                # 计算波形数据 (简化版，仅计算音量包络)
                # 为了性能，我们采样音频以获取约1000个数据点
                samples = min(1000, len(audio))
                step = max(1, len(audio) // samples)
                
                self.waveform_data = []
                for i in range(0, len(audio), step):
                    segment = audio[i:i+step]
                    if len(segment) > 0:
                        # 获取音量并归一化
                        volume = min(1.0, abs(segment.dBFS + 80) / 80)
                        self.waveform_data.append(volume)
                
                # 设置波形显示
                self.waveform_display.set_waveform_data(self.waveform_data)
                
                # 重置播放位置
                self.current_position_ms = 0
                self.position_label.setText("00:00")
                self.waveform_display.set_position(0)
                
                return True
            else:
                print("警告: pydub库不可用，无法分析波形")
                return False
                
        except Exception as e:
            print(f"加载音频文件出错: {str(e)}")
            return False
            
    def toggle_playback(self):
        """切换播放/暂停状态"""
        if not self.audio_file:
            return
            
        if self.is_playing:
            self.pause()
        else:
            self.play()
            
    def play(self):
        """开始播放"""
        if not self.audio_file or self.is_playing:
            return
            
        # 如果已经到达末尾，重置位置
        if self.current_position_ms >= self.audio_length_ms:
            self.current_position_ms = 0
            
        # 更新UI
        self.is_playing = True
        pause_pixmap = create_svg_pixmap(SVG_ICONS["pause"], 20, COLORS["text_primary"])
        self.play_button.setIcon(pause_pixmap)
        
        # 启动计时器
        self.playback_timer.start()
        
        # 发出信号
        self.playbackStarted.emit()
        
    def pause(self):
        """暂停播放"""
        if not self.is_playing:
            return
            
        # 更新UI
        self.is_playing = False
        play_pixmap = create_svg_pixmap(SVG_ICONS["play"], 20, COLORS["text_primary"])
        self.play_button.setIcon(play_pixmap)
        
        # 停止计时器
        self.playback_timer.stop()
        
        # 发出信号
        self.playbackPaused.emit()
        
    def stop(self):
        """停止播放并重置位置"""
        # 暂停播放
        self.pause()
        
        # 重置位置
        self.current_position_ms = 0
        self.position_label.setText("00:00")
        self.waveform_display.set_position(0)
        
        # 发出信号
        self.playbackStopped.emit()
        
    def seek_to_position(self, ratio):
        """跳转到指定位置"""
        if not self.audio_file:
            return
            
        # 计算新位置
        new_position_ms = int(self.audio_length_ms * ratio)
        self.current_position_ms = new_position_ms
        
        # 更新UI
        position_str = self._format_time(self.current_position_ms)
        self.position_label.setText(position_str)
        
        # 发出信号
        self.playbackPositionChanged.emit(self.current_position_ms)
        
    def _update_playback_position(self):
        """更新播放位置"""
        if not self.is_playing:
            return
            
        # 模拟播放进度，每次更新加50毫秒（与计时器间隔一致）
        self.current_position_ms += 50
        
        # 检查是否播放完毕
        if self.current_position_ms >= self.audio_length_ms:
            self.stop()
            return
            
        # 更新UI
        position_ratio = self.current_position_ms / self.audio_length_ms
        self.waveform_display.set_position(position_ratio)
        
        position_str = self._format_time(self.current_position_ms)
        self.position_label.setText(position_str)
        
        # 发出信号
        self.playbackPositionChanged.emit(self.current_position_ms)
        
    def _format_time(self, ms):
        """格式化时间为MM:SS格式"""
        total_seconds = ms // 1000
        minutes = total_seconds // 60
        seconds = total_seconds % 60
        return f"{minutes:02d}:{seconds:02d}" 