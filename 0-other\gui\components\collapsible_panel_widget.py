#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可折叠面板组件 - 支持动画效果的折叠面板
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
    QFrame, QSizePolicy, QCheckBox
)
from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QEasingCurve, QParallelAnimationGroup
from PySide6.QtGui import QFont


class CollapsiblePanelWidget(QFrame):
    """可折叠面板组件"""
    
    # 信号定义
    toggled = Signal(bool)  # 折叠状态变化信号
    
    def __init__(self, title="", expanded=False, parent=None):
        super().__init__(parent)
        
        # 状态变量
        self.is_expanded = expanded
        self.animation_duration = 300  # 动画持续时间（毫秒）
        
        self.init_ui(title)
        self.setup_animation()
        
        # 设置初始状态
        if not expanded:
            self.content_widget.setMaximumHeight(0)
            
    def init_ui(self, title):
        """初始化UI"""
        self.setFrameStyle(QFrame.StyledPanel)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建标题栏
        self.header_widget = self.create_header(title)
        main_layout.addWidget(self.header_widget)
        
        # 创建内容区域
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(16, 8, 16, 16)
        self.content_layout.setSpacing(8)
        
        main_layout.addWidget(self.content_widget)
        
    def create_header(self, title):
        """创建标题栏"""
        header = QWidget()
        header.setStyleSheet("""
            QWidget {
                background-color: rgba(57, 75, 97, 0.5);
                border-radius: 4px;
            }
            QWidget:hover {
                background-color: rgba(69, 90, 121, 0.3);
            }
        """)
        header.setCursor(Qt.PointingHandCursor)
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(12)
        
        # 标题文本
        self.title_label = QLabel(title)
        self.title_label.setProperty("class", "title")
        font = QFont()
        font.setBold(True)
        font.setPointSize(14)
        self.title_label.setFont(font)
        layout.addWidget(self.title_label)
        
        # 弹性空间
        layout.addStretch()
        
        # 展开/折叠图标
        self.toggle_icon = QLabel("▼")
        self.toggle_icon.setAlignment(Qt.AlignCenter)
        self.toggle_icon.setStyleSheet("font-size: 12px; color: #B0BEC5;")
        self.toggle_icon.setFixedSize(20, 20)
        layout.addWidget(self.toggle_icon)
        
        # 点击事件
        header.mousePressEvent = lambda event: self.on_header_clicked(event)
        
        return header
        
    def create_header_with_checkboxes(self, title, checkboxes=None):
        """创建带复选框的标题栏"""
        header = QWidget()
        header.setStyleSheet("""
            QWidget {
                background-color: rgba(57, 75, 97, 0.5);
                border-radius: 4px;
            }
            QWidget:hover {
                background-color: rgba(69, 90, 121, 0.3);
            }
        """)
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(12)
        
        # 标题文本
        self.title_label = QLabel(title)
        self.title_label.setProperty("class", "title")
        font = QFont()
        font.setBold(True)
        font.setPointSize(14)
        self.title_label.setFont(font)
        layout.addWidget(self.title_label)
        
        # 弹性空间
        layout.addStretch()
        
        # 添加复选框
        if checkboxes:
            for checkbox_text, checked in checkboxes:
                checkbox = QCheckBox(checkbox_text)
                checkbox.setChecked(checked)
                checkbox.setProperty("class", "secondary")
                # 阻止事件冒泡到父级
                checkbox.mousePressEvent = lambda e: e.accept()
                layout.addWidget(checkbox)
        
        # 展开/折叠图标
        self.toggle_icon = QLabel("▼")
        self.toggle_icon.setAlignment(Qt.AlignCenter)
        self.toggle_icon.setStyleSheet("font-size: 12px; color: #B0BEC5;")
        self.toggle_icon.setFixedSize(20, 20)
        layout.addWidget(self.toggle_icon)
        
        # 点击事件（只对非复选框区域有效）
        header.mousePressEvent = lambda event: self.on_header_clicked(event)
        
        return header
        
    def setup_animation(self):
        """设置动画"""
        self.animation = QPropertyAnimation(self.content_widget, b"maximumHeight")
        self.animation.setDuration(self.animation_duration)
        self.animation.setEasingCurve(QEasingCurve.InOutCubic)
        
    def on_header_clicked(self, event):
        """标题栏点击事件"""
        if event.button() == Qt.LeftButton:
            self.toggle()
            
    def toggle(self):
        """切换展开/折叠状态"""
        if self.is_expanded:
            self.collapse()
        else:
            self.expand()
            
    def expand(self):
        """展开面板"""
        if self.is_expanded:
            return
            
        self.is_expanded = True
        
        # 计算内容高度
        content_height = self.content_widget.sizeHint().height()
        
        # 设置动画
        self.animation.setStartValue(0)
        self.animation.setEndValue(content_height)
        
        # 图标旋转
        self.toggle_icon.setText("▲")
        
        # 开始动画
        self.animation.start()
        
        self.toggled.emit(True)
        
    def collapse(self):
        """折叠面板"""
        if not self.is_expanded:
            return
            
        self.is_expanded = False
        
        # 设置动画
        current_height = self.content_widget.height()
        self.animation.setStartValue(current_height)
        self.animation.setEndValue(0)
        
        # 图标旋转
        self.toggle_icon.setText("▼")
        
        # 开始动画
        self.animation.start()
        
        self.toggled.emit(False)
        
    def add_content_widget(self, widget):
        """添加内容控件"""
        self.content_layout.addWidget(widget)
        
    def add_content_layout(self, layout):
        """添加内容布局"""
        self.content_layout.addLayout(layout)
        
    def set_title(self, title):
        """设置标题"""
        self.title_label.setText(title)
        
    def get_content_layout(self):
        """获取内容布局"""
        return self.content_layout
        
    def set_expanded(self, expanded):
        """设置展开状态（无动画）"""
        self.is_expanded = expanded
        if expanded:
            self.content_widget.setMaximumHeight(16777215)  # 最大高度
            self.toggle_icon.setText("▲")
        else:
            self.content_widget.setMaximumHeight(0)
            self.toggle_icon.setText("▼")
