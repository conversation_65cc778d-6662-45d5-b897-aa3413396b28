"""
加载指示器组件 - 提供旋转动画效果的加载指示器
"""

from PySide6.QtCore import Qt, QSize, QTimer, QRect
from PySide6.QtGui import QPainter, QPen, QColor, QBrush
from PySide6.QtWidgets import QWidget


class LoadingIndicator(QWidget):
    """旋转动画的加载指示器组件"""

    def __init__(self, parent=None, color="#4CAFFF", size=24, line_width=3):
        """
        初始化加载指示器
        
        Args:
            parent: 父组件
            color: 动画颜色，默认为浅蓝色
            size: 指示器大小，默认24像素
            line_width: 线条宽度，默认3像素
        """
        super().__init__(parent)
        
        self.angle = 0  # 当前旋转角度
        self.color = QColor(color)
        self.line_width = line_width
        self.timer = QTimer(self)
        self.timer.timeout.connect(self._rotate)
        self.is_active = False
        
        # 设置指示器大小
        self.indicator_size = size
        self.setFixedSize(QSize(size, size))
        
    def start(self):
        """开始动画"""
        if not self.is_active:
            self.timer.start(50)  # 每50毫秒更新一次，约20fps
            self.is_active = True
            self.show()
            
    def stop(self):
        """停止动画"""
        self.timer.stop()
        self.is_active = False
        self.hide()
        
    def isActive(self):
        """返回指示器是否处于活动状态"""
        return self.is_active
        
    def _rotate(self):
        """更新旋转角度并重绘"""
        self.angle = (self.angle + 10) % 360
        self.update()
        
    def paintEvent(self, event):
        """绘制指示器"""
        if not self.is_active:
            return
            
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 设置画笔
        pen = QPen(self.color)
        pen.setWidth(self.line_width)
        pen.setCapStyle(Qt.RoundCap)
        painter.setPen(pen)
        
        # 计算绘制区域
        width, height = self.width(), self.height()
        rect = QRect(self.line_width, self.line_width, 
                    width - 2 * self.line_width, 
                    height - 2 * self.line_width)
        
        # 绘制弧线，角度从0到270度
        start_angle = self.angle * 16  # QPainter使用16*角度单位
        span_angle = 270 * 16  # 3/4圆
        
        painter.drawArc(rect, start_angle, span_angle)
        
    def sizeHint(self):
        """建议尺寸"""
        return QSize(self.indicator_size, self.indicator_size)


class PulsingLoadingIndicator(QWidget):
    """脉动效果的加载指示器组件"""

    def __init__(self, parent=None, color="#4CAFFF", size=30):
        """
        初始化脉动加载指示器
        
        Args:
            parent: 父组件
            color: 动画颜色，默认为浅蓝色
            size: 指示器大小，默认30像素
        """
        super().__init__(parent)
        
        self.scale = 0.5  # 当前缩放比例
        self.scale_direction = 0.05  # 缩放方向和速度
        self.color = QColor(color)
        self.timer = QTimer(self)
        self.timer.timeout.connect(self._pulse)
        self.is_active = False
        
        # 设置指示器大小
        self.indicator_size = size
        self.setFixedSize(QSize(size, size))
        
    def start(self):
        """开始动画"""
        if not self.is_active:
            self.timer.start(60)  # 每60毫秒更新一次
            self.is_active = True
            self.show()
            
    def stop(self):
        """停止动画"""
        self.timer.stop()
        self.is_active = False
        self.hide()
        
    def isActive(self):
        """返回指示器是否处于活动状态"""
        return self.is_active
        
    def _pulse(self):
        """更新脉动效果并重绘"""
        self.scale += self.scale_direction
        
        # 在0.5到1.0之间变化
        if self.scale >= 1.0:
            self.scale = 1.0
            self.scale_direction = -0.05
        elif self.scale <= 0.5:
            self.scale = 0.5
            self.scale_direction = 0.05
            
        self.update()
        
    def paintEvent(self, event):
        """绘制指示器"""
        if not self.is_active:
            return
            
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 设置画刷
        brush = QBrush(self.color)
        painter.setBrush(brush)
        painter.setPen(Qt.NoPen)
        
        # 计算绘制区域
        width, height = self.width(), self.height()
        center_x, center_y = width // 2, height // 2
        
        # 计算当前半径
        max_radius = min(width, height) // 2
        current_radius = max_radius * self.scale
        
        # 绘制圆形
        painter.drawEllipse(center_x - current_radius, 
                           center_y - current_radius,
                           current_radius * 2,
                           current_radius * 2)
        
    def sizeHint(self):
        """建议尺寸"""
        return QSize(self.indicator_size, self.indicator_size) 