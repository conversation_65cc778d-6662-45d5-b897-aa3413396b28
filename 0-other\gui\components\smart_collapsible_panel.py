#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能折叠面板组件 - 支持互斥展开和智能布局
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QSizePolicy, QGraphicsOpacityEffect
)
from PySide6.QtCore import Qt, QPropertyAnimation, QEasingCurve, Signal, QTimer
from PySide6.QtGui import QFont, QPainter, QPen, QPixmap
import math


class SmartCollapsiblePanel(QFrame):
    """智能折叠面板 - 支持互斥展开和动画效果"""
    
    # 信号
    expansion_changed = Signal(bool, str)  # 展开状态变化信号 (是否展开, 面板ID)
    
    def __init__(self, title: str, panel_id: str = None, expanded: bool = False, parent=None):
        super().__init__(parent)
        
        self.title = title
        self.panel_id = panel_id or title
        self.is_expanded = expanded
        self.content_widget = None
        self.panel_group = None  # 面板组管理器
        
        # 动画相关
        self.animation_duration = 300
        self.content_height = 0
        
        self.setup_ui()
        self.setup_animations()
        
    def setup_ui(self):
        """设置UI"""
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet("""
            SmartCollapsiblePanel {
                background-color: #2a2a2a;
                border: 1px solid #3a3a3a;
                border-radius: 12px;
                margin: 4px 0px;
            }
            SmartCollapsiblePanel:hover {
                border-color: #4a4a4a;
            }
        """)
        
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建标题栏
        self.create_header()
        
        # 创建内容容器
        self.content_container = QFrame()
        self.content_container.setFrameStyle(QFrame.NoFrame)
        self.content_container.setStyleSheet("background-color: transparent;")
        
        self.content_layout = QVBoxLayout(self.content_container)
        self.content_layout.setContentsMargins(20, 0, 20, 20)
        self.content_layout.setSpacing(12)
        
        self.main_layout.addWidget(self.content_container)
        
        # 设置初始状态
        if not self.is_expanded:
            self.content_container.hide()
            
    def create_header(self):
        """创建标题栏"""
        self.header_widget = QFrame()
        self.header_widget.setFrameStyle(QFrame.NoFrame)
        self.header_widget.setStyleSheet("""
            QFrame {
                background-color: transparent;
                border: none;
                border-radius: 12px;
                padding: 16px 20px;
            }
            QFrame:hover {
                background-color: rgba(255, 255, 255, 0.05);
            }
        """)
        self.header_widget.setCursor(Qt.PointingHandCursor)
        
        header_layout = QHBoxLayout(self.header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(12)
        
        # 展开/折叠图标
        self.expand_icon = QLabel()
        self.expand_icon.setFixedSize(16, 16)
        self.expand_icon.setAlignment(Qt.AlignCenter)
        self.update_icon()
        
        # 标题标签
        self.title_label = QLabel(self.title)
        self.title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 600;
                background-color: transparent;
                border: none;
            }
        """)
        
        # 状态指示器（可选）
        self.status_label = QLabel()
        self.status_label.setStyleSheet("""
            QLabel {
                color: #8a8a8a;
                font-size: 12px;
                font-weight: 400;
                background-color: transparent;
                border: none;
            }
        """)
        
        header_layout.addWidget(self.expand_icon)
        header_layout.addWidget(self.title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.status_label)
        
        self.main_layout.addWidget(self.header_widget)
        
        # 连接点击事件
        self.header_widget.mousePressEvent = self.on_header_clicked
        
    def setup_animations(self):
        """设置动画"""
        self.expand_animation = QPropertyAnimation(self.content_container, b"maximumHeight")
        self.expand_animation.setDuration(self.animation_duration)
        self.expand_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        self.opacity_animation = QPropertyAnimation()
        self.opacity_animation.setDuration(self.animation_duration // 2)
        
    def update_icon(self):
        """更新展开/折叠图标"""
        if self.is_expanded:
            # 向下箭头 (展开状态)
            self.expand_icon.setText("▼")
            self.expand_icon.setStyleSheet("""
                QLabel {
                    color: #007AFF;
                    font-size: 12px;
                    font-weight: bold;
                    background-color: transparent;
                }
            """)
        else:
            # 向右箭头 (折叠状态)
            self.expand_icon.setText("▶")
            self.expand_icon.setStyleSheet("""
                QLabel {
                    color: #8a8a8a;
                    font-size: 12px;
                    font-weight: bold;
                    background-color: transparent;
                }
            """)
        
    def on_header_clicked(self, event):
        """标题栏点击事件"""
        if event.button() == Qt.LeftButton:
            self.toggle()
            
    def toggle(self):
        """切换展开/折叠状态"""
        if self.is_expanded:
            self.collapse()
        else:
            self.expand()
            
    def expand(self):
        """展开面板"""
        if self.is_expanded:
            return
            
        # 通知面板组（如果存在）
        if self.panel_group:
            self.panel_group.expand_panel(self.panel_id)
            return
            
        self._do_expand()
        
    def collapse(self):
        """折叠面板"""
        if not self.is_expanded:
            return
            
        self._do_collapse()
        
    def _do_expand(self):
        """执行展开动画"""
        self.is_expanded = True
        self.update_icon()
        
        # 显示内容容器
        self.content_container.show()
        
        # 计算内容高度
        self.content_container.adjustSize()
        target_height = self.content_container.sizeHint().height()
        
        # 设置动画
        self.content_container.setMaximumHeight(0)
        self.expand_animation.setStartValue(0)
        self.expand_animation.setEndValue(target_height)
        self.expand_animation.start()
        
        # 发送信号
        self.expansion_changed.emit(True, self.panel_id)
        
    def _do_collapse(self):
        """执行折叠动画"""
        self.is_expanded = False
        self.update_icon()
        
        # 获取当前高度
        current_height = self.content_container.height()
        
        # 设置动画
        self.expand_animation.setStartValue(current_height)
        self.expand_animation.setEndValue(0)
        self.expand_animation.finished.connect(self._on_collapse_finished)
        self.expand_animation.start()
        
        # 发送信号
        self.expansion_changed.emit(False, self.panel_id)
        
    def _on_collapse_finished(self):
        """折叠动画完成"""
        self.content_container.hide()
        self.expand_animation.finished.disconnect()
        
    def add_content_widget(self, widget):
        """添加内容控件"""
        self.content_widget = widget
        self.content_layout.addWidget(widget)
        
    def add_content_layout(self, layout):
        """添加内容布局"""
        self.content_layout.addLayout(layout)
        
    def set_panel_group(self, panel_group):
        """设置面板组"""
        self.panel_group = panel_group
        
    def set_status(self, status_text: str):
        """设置状态文本"""
        self.status_label.setText(status_text)
        
    def get_panel_id(self):
        """获取面板ID"""
        return self.panel_id


class SmartPanelGroup:
    """智能面板组管理器 - 管理互斥展开"""
    
    def __init__(self, exclusive_mode: bool = True):
        self.panels = {}
        self.exclusive_mode = exclusive_mode  # 是否互斥模式
        self.expanded_panel = None
        
    def add_panel(self, panel: SmartCollapsiblePanel):
        """添加面板到组"""
        panel_id = panel.get_panel_id()
        self.panels[panel_id] = panel
        panel.set_panel_group(self)
        
        # 连接信号
        panel.expansion_changed.connect(self.on_panel_expansion_changed)
        
    def expand_panel(self, panel_id: str):
        """展开指定面板"""
        if panel_id not in self.panels:
            return
            
        target_panel = self.panels[panel_id]
        
        # 如果是互斥模式，先折叠其他面板
        if self.exclusive_mode and self.expanded_panel and self.expanded_panel != panel_id:
            self.panels[self.expanded_panel]._do_collapse()
            
        # 展开目标面板
        target_panel._do_expand()
        self.expanded_panel = panel_id
        
    def collapse_all(self):
        """折叠所有面板"""
        for panel in self.panels.values():
            if panel.is_expanded:
                panel._do_collapse()
        self.expanded_panel = None
        
    def on_panel_expansion_changed(self, expanded: bool, panel_id: str):
        """面板展开状态变化处理"""
        if expanded:
            self.expanded_panel = panel_id
        elif self.expanded_panel == panel_id:
            self.expanded_panel = None
