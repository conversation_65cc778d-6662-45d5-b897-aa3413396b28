"""
滑块组件模块 - 提供带有标签和当前值显示的滑块控件
"""

from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import QWidget, QSlider, QLabel, QVBoxLayout, QHBoxLayout, QSizePolicy

from src.utils.style import COLORS


class LabeledSlider(QWidget):
    """带标签和值显示的自定义滑块组件"""
    
    # 自定义信号
    valueChanged = Signal(int)
    
    def __init__(self, title="", min_value=0, max_value=100, value=0, parent=None):
        super().__init__(parent)
        
        self._min_value = min_value
        self._max_value = max_value
        self._title = title
        
        self._setup_ui()
        self.set_value(value)
        
    def _setup_ui(self):
        """设置组件UI"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(10)
        
        # 顶部布局，包含标题和值
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        self.title_label = QLabel(self._title)
        self.title_label.setStyleSheet(f"color: {COLORS['text_secondary']}; font-size: 14px;")
        header_layout.addWidget(self.title_label)
        
        header_layout.addStretch()
        
        self.value_label = QLabel(str(self._min_value))
        self.value_label.setStyleSheet(f"color: {COLORS['text_primary']}; font-size: 14px; font-weight: bold;")
        header_layout.addWidget(self.value_label)
        
        main_layout.addLayout(header_layout)
        
        # 滑块
        self.slider = QSlider(Qt.Horizontal)
        self.slider.setMinimum(self._min_value)
        self.slider.setMaximum(self._max_value)
        self.slider.setValue(self._min_value)
        self.slider.setTracking(True)
        self.slider.valueChanged.connect(self._on_slider_value_changed)
        
        # 自定义滑块样式
        self.slider.setStyleSheet(f"""
            QSlider::groove:horizontal {{
                height: 6px;
                background: {COLORS["bg_dark_tertiary"]};
                border-radius: 3px;
            }}
            
            QSlider::handle:horizontal {{
                background: {COLORS["accent_blue"]};
                border: 3px solid {COLORS["bg_dark_tertiary"]};
                width: 18px;
                height: 18px;
                margin: -6px 0;
                border-radius: 9px;
            }}
        """)
        
        # 设置滑块大小策略，让它可以水平拉伸
        slider_size_policy = QSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.slider.setSizePolicy(slider_size_policy)
        
        main_layout.addWidget(self.slider)
        
    def _on_slider_value_changed(self, value):
        """当滑块值改变时更新标签并发出信号"""
        self.value_label.setText(str(value))
        self.valueChanged.emit(value)
        
    def set_value(self, value):
        """设置滑块值"""
        self.slider.setValue(value)
        
    def value(self):
        """获取当前滑块值"""
        return self.slider.value()
        
    def set_range(self, min_value, max_value):
        """设置滑块范围"""
        self._min_value = min_value
        self._max_value = max_value
        self.slider.setMinimum(min_value)
        self.slider.setMaximum(max_value) 