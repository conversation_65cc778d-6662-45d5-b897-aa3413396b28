# DDSP歌声合成GUI

DDSP歌声合成GUI是一个基于PySide6的桌面应用程序，提供了直观的用户界面来使用DDSP（可微分数字信号处理）进行歌声合成。该应用程序设计用于帮助音乐制作者和爱好者轻松地进行AI歌声翻唱，无需深入了解底层技术。

![应用截图](./screenshots/app_preview.png)

## 功能特性

- **直观的用户界面**：暗色主题设计，符合专业音频工具的视觉风格
- **文件拖放功能**：支持直接拖放音频文件进行处理
- **音色模型选择**：内置多种音色模型，支持自定义模型导入
- **音高调节**：精确控制输出歌声的音高参数
- **混响与和声设置**：添加专业级混响效果和和声生成
- **高级音频参数配置**：提供采样率、比特率等高级设置
- **API服务管理**：内置API服务管理界面，支持本地和云端API切换
- **成品歌曲管理**：轻松管理和访问生成的歌曲作品
- **实时处理状态反馈**：清晰展示处理进度和状态

## 技术架构

- **前端界面**：使用PySide6（Qt for Python）构建响应式UI
- **组件化设计**：遵循组件化原则，易于扩展和维护
- **SVG图标系统**：使用可缩放的矢量图标，确保在任何分辨率下的清晰度
- **自定义组件**：包含滑块、拖放区、可折叠面板等自定义组件
- **后端API集成**：可连接本地或远程的DDSP处理服务API

## 安装说明

### 环境要求

- Python 3.8+
- PySide6
- 其他依赖库（详见requirements.txt）

### 安装步骤

1. 克隆仓库到本地：

```bash
git clone https://github.com/yourusername/ddsp-gui.git
cd ddsp-gui
```

2. 创建并激活虚拟环境：

```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. 安装依赖：

```bash
pip install -r requirements.txt
```

4. 启动应用：

```bash
python src/main.py
```

## 使用说明

### 歌曲翻唱

1. 在"歌曲制作"标签页中，拖放你想要处理的音频文件到上传区
2. 选择处理模式（完整模式或干声模式）
3. 选择输出格式（WAV或MP3）
4. 调整右侧面板中的音色模型、音高和其他音频参数
5. 确保API服务已启动（查看底部状态栏）
6. 点击"一键翻唱"按钮开始处理
7. 等待处理完成，结果会自动添加到"我的成品歌曲"列表

### API管理

1. 切换到"API管理"标签页
2. 设置API URL和Python环境路径
3. 点击"启动API"按钮启动服务
4. 查看控制台输出了解服务状态
5. 完成使用后，点击"停止API"按钮关闭服务

## 项目结构

```
src/
├── components/       # UI组件
│   ├── collapsible_panel.py  # 可折叠面板
│   ├── drop_zone.py          # 文件拖放区
│   ├── icons.py              # SVG图标系统
│   ├── loading_indicator.py  # 加载动画指示器
│   ├── slider.py             # 自定义滑块
│   └── panels/              # 功能面板组件
│       ├── advanced_audio_panel.py  # 高级音频参数面板
│       ├── inference_panel.py       # 推理参数配置面板
│       ├── left_panel.py            # 左侧面板
│       ├── model_pitch_panel.py     # 模型与音高面板
│       └── reverb_panel.py          # 混响与和声面板
├── pages/            # 页面
│   ├── api_management_page.py  # API管理页面
│   └── song_production_page.py # 歌曲制作页面
├── utils/            # 工具类
│   └── style.py      # 全局样式定义
├── resources/        # 资源文件
│   └── models/       # 预训练模型
├── app.py            # 主窗口
└── main.py           # 程序入口
```

## 许可证

MIT License

## 贡献指南

欢迎提交Issues和Pull Requests来帮助改进这个项目！

## 致谢

- DDSP团队提供的底层技术支持
- Qt/PySide6团队提供的UI框架
- 所有贡献者和测试者 