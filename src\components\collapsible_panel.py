"""
折叠面板组件模块 - 提供可展开/折叠的面板控件
用于实现"混响与和声"，"高级音频参数"等可折叠面板
"""

from PySide6.QtCore import Qt, QPropertyAnimation, QRect, QSize, QEasingCurve, Property, Signal
from PySide6.QtWidgets import (
    QWidget, QFrame, QVBoxLayout, QHBoxLayout, 
    QPushButton, QLabel, QScrollArea, QSizePolicy
)
from PySide6.QtGui import QTransform

from src.utils.style import COLORS, COLLAPSIBLE_BUTTON_STYLE
from src.components.icons import SVG_ICONS, create_svg_pixmap

class CollapsiblePanel(QWidget):
    """可折叠/展开的面板组件"""
    
    # 当面板展开/折叠状态变化时发出的信号
    panelToggled = Signal(str, bool)  # (面板标题, 是否展开)
    
    def __init__(self, title="", enable_close_text=False, parent=None):
        super().__init__(parent)
        self._is_expanded = False  # 初始状态为折叠
        self._title = title
        self._enable_close_text = enable_close_text  # 是否启用"点击关闭"文本变化
        self._arrow_rotation = 0  # 箭头旋转角度
        
        # 设置尺寸策略，使组件能够随窗口大小变化
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        
        self._setup_ui()
        
    def _setup_ui(self):
        """设置组件UI"""
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建标题部分
        self._setup_header()
        
        # 创建内容区域
        self._setup_content()
        
    def _setup_header(self):
        """设置面板标题区域"""
        # 标题栏框架
        self.header_frame = QFrame()
        self.header_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS["bg_dark_tertiary"]};
                border-radius: 8px;
            }}
        """)
        self.header_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        header_layout = QHBoxLayout(self.header_frame)
        header_layout.setContentsMargins(16, 12, 16, 12)
        
        # 标题文本
        self.title_label = QLabel(self._title)
        self.title_label.setStyleSheet(f"""
            QLabel {{
                color: {COLORS["text_primary"]};
                font-size: 16px;
                font-weight: 600;
            }}
        """)
        self.title_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        header_layout.addWidget(self.title_label)
        
        header_layout.addStretch()
        
        # 箭头图标
        self.arrow_icon = QLabel()
        arrow_pixmap = create_svg_pixmap(SVG_ICONS["arrow_down"], 20, COLORS["text_primary"])
        self.arrow_icon.setPixmap(arrow_pixmap)
        header_layout.addWidget(self.arrow_icon)
        
        # 使整个标题栏可点击
        self.header_frame.mousePressEvent = self._toggle_expanded
        self.header_frame.setCursor(Qt.PointingHandCursor)
        
        # 添加到主布局
        self.main_layout.addWidget(self.header_frame)
        
    def _setup_content(self):
        """设置面板内容区域"""
        # 内容滚动区域
        self.content_scroll = QScrollArea()
        self.content_scroll.setWidgetResizable(True)
        self.content_scroll.setFrameShape(QFrame.NoFrame)
        self.content_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.content_scroll.setMaximumHeight(0)  # 初始状态为折叠
        self.content_scroll.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 内容容器
        self.content_widget = QWidget()
        self.content_widget.setStyleSheet(f"background-color: {COLORS['bg_dark_secondary']};")
        self.content_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(16, 16, 16, 16)
        self.content_layout.setSpacing(16)
        
        # 将内容容器设置为滚动区域的部件
        self.content_scroll.setWidget(self.content_widget)
        
        # 添加到主布局
        self.main_layout.addWidget(self.content_scroll)
        
        # 存储内容的最大高度，用于动画
        self._max_content_height = 400  # 默认最大高度
    
    # 定义属性访问器
    def get_arrow_rotation(self):
        return self._arrow_rotation
    
    def set_arrow_rotation(self, angle):
        self._arrow_rotation = angle
        self._update_arrow_rotation()
    
    # 创建属性
    arrow_rotation = Property(float, get_arrow_rotation, set_arrow_rotation)
    
    def _update_arrow_rotation(self):
        """根据当前旋转角度更新箭头图标"""
        transform = QTransform()
        transform.rotate(self._arrow_rotation)
        arrow_pixmap = create_svg_pixmap(SVG_ICONS["arrow_down"], 20, COLORS["text_primary"])
        rotated_pixmap = arrow_pixmap.transformed(transform, Qt.SmoothTransformation)
        self.arrow_icon.setPixmap(rotated_pixmap)
        
    def _toggle_expanded(self, event=None):
        """切换展开/折叠状态"""
        self._is_expanded = not self._is_expanded

        # 直接设置目标高度，不使用动画
        target_height = self._max_content_height if self._is_expanded else 0
        self.content_scroll.setMaximumHeight(target_height)

        # 更新标题文本和样式
        self._update_header_appearance()

        # 旋转箭头图标
        self._rotate_arrow()

        # 发出状态变化信号
        self.panelToggled.emit(self._title, self._is_expanded)

    def _update_header_appearance(self):
        """更新标题栏的外观（文本和样式）"""
        if self._enable_close_text and self._is_expanded:
            # 展开状态：显示"点击关闭"，使用主要按钮颜色（仅当启用时）
            self.title_label.setText("点击关闭")
            self.header_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {COLORS["accent_blue"]};
                    border-radius: 8px;
                }}
            """)
        else:
            # 折叠状态或未启用：显示原始标题，使用默认颜色
            self.title_label.setText(self._title)
            self.header_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {COLORS["bg_dark_tertiary"]};
                    border-radius: 8px;
                }}
            """)
    
    def _rotate_arrow(self):
        """旋转箭头图标"""
        # 创建旋转动画
        self.arrow_animation = QPropertyAnimation(self, b"arrow_rotation")
        self.arrow_animation.setDuration(10)  # 减少动画时间为200毫秒
        self.arrow_animation.setEasingCurve(QEasingCurve.InOutQuad)  # 使用更平滑的缓动曲线
        self.arrow_animation.setStartValue(0 if self._is_expanded else 180)
        self.arrow_animation.setEndValue(180 if self._is_expanded else 0)
        self.arrow_animation.start()
        
    def set_content_layout(self, layout):
        """设置面板内容布局"""
        # 清除现有布局中的所有项
        while self.content_layout.count():
            item = self.content_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
                
        # 添加新的布局
        self.content_layout.addLayout(layout)
        
    def add_widget(self, widget):
        """添加控件到面板内容区域"""
        self.content_layout.addWidget(widget)
        
    def add_layout(self, layout):
        """添加布局到面板内容区域"""
        self.content_layout.addLayout(layout)
        
    def set_expanded(self, expanded):
        """直接设置展开/折叠状态"""
        if self._is_expanded != expanded:
            self._toggle_expanded()
            
    def is_expanded(self):
        """返回面板是否处于展开状态"""
        return self._is_expanded
        
    def get_title(self):
        """获取面板标题"""
        return self._title
        
    def set_max_content_height(self, height):
        """设置内容区域最大高度"""
        self._max_content_height = height
        if self._is_expanded:
            self.content_scroll.setMaximumHeight(height)
    
    def resizeEvent(self, event):
        """处理大小调整事件"""
        super().resizeEvent(event)
        # 如果面板处于展开状态，根据当前窗口大小调整内容高度
        if self._is_expanded:
            # 根据当前窗口高度动态调整内容区域最大高度
            # 这里使用窗口高度的一半作为最大高度，可以根据需要调整
            new_height = min(self.window().height() // 2, 600)  # 最大不超过600
            self._max_content_height = new_height
            self.content_scroll.setMaximumHeight(new_height) 