#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化开关组件 - iOS风格开关
"""

from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel, QVBoxLayout
from PySide6.QtCore import Qt, QPropertyAnimation, QEasingCurve, Signal, QRect
from PySide6.QtGui import QPainter, QBrush, QPen, QColor, QFont
import math


class ModernSwitch(QWidget):
    """现代化iOS风格开关"""

    # 信号
    toggled = Signal(bool)
    
    def __init__(self, text: str = "", checked: bool = False, parent=None):
        super().__init__(parent)
        
        self.text = text
        self.checked = checked
        self.enabled = True
        
        # 样式参数
        self.switch_width = 50
        self.switch_height = 28
        self.thumb_size = 24
        self.animation_duration = 200
        
        # 颜色定义
        self.bg_color_off = QColor("#3a3a3a")
        self.bg_color_on = QColor("#007AFF")
        self.thumb_color = QColor("#ffffff")
        self.border_color = QColor("#4a4a4a")
        
        # 动画属性
        self.thumb_position = 0.0  # 0.0 = 左侧, 1.0 = 右侧
        
        self.setup_ui()
        self.setup_animations()
        
        # 设置初始状态
        if self.checked:
            self.thumb_position = 1.0
            
    def setup_ui(self):
        """设置UI"""
        self.setFixedHeight(self.switch_height + 10)  # 额外空间用于文本
        
        if self.text:
            # 如果有文本，创建水平布局
            layout = QHBoxLayout(self)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(12)
            
            # 文本标签
            self.text_label = QLabel(self.text)
            self.text_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: 500;
                    background-color: transparent;
                }
            """)
            
            layout.addWidget(self.text_label)
            layout.addStretch()
            
            # 开关区域
            switch_widget = QWidget()
            switch_widget.setFixedSize(self.switch_width, self.switch_height)
            switch_widget.paintEvent = self.paint_switch
            switch_widget.mousePressEvent = self.mouse_press_event
            
            layout.addWidget(switch_widget)
            self.switch_widget = switch_widget
        else:
            # 没有文本，直接设置开关大小
            self.setFixedSize(self.switch_width, self.switch_height)
            self.switch_widget = self
            
    def setup_animations(self):
        """设置动画"""
        self.animation = QPropertyAnimation(self, b"thumbPosition")
        self.animation.setDuration(self.animation_duration)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        self.animation.valueChanged.connect(self.update)
        
    def paintEvent(self, event):
        """绘制事件"""
        if hasattr(self, 'switch_widget') and self.switch_widget != self:
            super().paintEvent(event)
        else:
            self.paint_switch(event)
            
    def paint_switch(self, event):
        """绘制开关"""
        painter = QPainter(self.switch_widget if hasattr(self, 'switch_widget') else self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 计算开关区域
        switch_rect = QRect(0, 0, self.switch_width, self.switch_height)
        
        # 背景颜色插值
        if self.checked:
            bg_color = self.bg_color_on
        else:
            # 根据动画进度插值颜色
            progress = self.thumb_position
            bg_color = self.interpolate_color(self.bg_color_off, self.bg_color_on, progress)
            
        # 绘制背景
        painter.setBrush(QBrush(bg_color))
        painter.setPen(QPen(self.border_color, 1))
        painter.drawRoundedRect(switch_rect, self.switch_height // 2, self.switch_height // 2)
        
        # 计算滑块位置
        thumb_margin = 2
        thumb_travel = self.switch_width - self.thumb_size - 2 * thumb_margin
        thumb_x = thumb_margin + thumb_travel * self.thumb_position
        thumb_y = (self.switch_height - self.thumb_size) // 2
        
        # 绘制滑块
        thumb_rect = QRect(int(thumb_x), thumb_y, self.thumb_size, self.thumb_size)
        painter.setBrush(QBrush(self.thumb_color))
        painter.setPen(QPen(QColor("#e0e0e0"), 1))
        painter.drawEllipse(thumb_rect)
        
        # 绘制滑块阴影效果
        shadow_color = QColor(0, 0, 0, 30)
        painter.setBrush(QBrush(shadow_color))
        painter.setPen(Qt.NoPen)
        shadow_rect = thumb_rect.adjusted(1, 1, 1, 1)
        painter.drawEllipse(shadow_rect)
        
    def interpolate_color(self, color1: QColor, color2: QColor, progress: float) -> QColor:
        """颜色插值"""
        progress = max(0.0, min(1.0, progress))
        
        r = int(color1.red() + (color2.red() - color1.red()) * progress)
        g = int(color1.green() + (color2.green() - color1.green()) * progress)
        b = int(color1.blue() + (color2.blue() - color1.blue()) * progress)
        a = int(color1.alpha() + (color2.alpha() - color1.alpha()) * progress)
        
        return QColor(r, g, b, a)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton and self.enabled:
            self.toggle()
            
    def mouse_press_event(self, event):
        """开关区域鼠标按下事件"""
        if event.button() == Qt.LeftButton and self.enabled:
            self.toggle()
            
    def toggle(self):
        """切换开关状态"""
        self.set_checked(not self.checked)
        
    def set_checked(self, checked: bool):
        """设置开关状态"""
        if self.checked == checked:
            return
            
        self.checked = checked
        
        # 启动动画
        start_pos = self.thumb_position
        end_pos = 1.0 if checked else 0.0
        
        self.animation.setStartValue(start_pos)
        self.animation.setEndValue(end_pos)
        self.animation.start()
        
        # 发送信号
        self.toggled.emit(checked)
        
    def is_checked(self) -> bool:
        """获取开关状态"""
        return self.checked
        
    def set_enabled(self, enabled: bool):
        """设置启用状态"""
        self.enabled = enabled
        self.setEnabled(enabled)
        
        # 更新样式
        if hasattr(self, 'text_label'):
            if enabled:
                self.text_label.setStyleSheet("""
                    QLabel {
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: 500;
                        background-color: transparent;
                    }
                """)
            else:
                self.text_label.setStyleSheet("""
                    QLabel {
                        color: #6a6a6a;
                        font-size: 14px;
                        font-weight: 500;
                        background-color: transparent;
                    }
                """)
        
        self.update()
        
    def get_thumb_position(self):
        """获取滑块位置（用于动画）"""
        return self.thumb_position
        
    def set_thumb_position(self, position: float):
        """设置滑块位置（用于动画）"""
        self.thumb_position = position
        self.update()
        
    # 属性定义（用于动画）
    thumbPosition = property(get_thumb_position, set_thumb_position)


class ModernSwitchGroup(QWidget):
    """现代化开关组 - 多个开关的容器"""
    
    def __init__(self, title: str = "", parent=None):
        super().__init__(parent)
        
        self.title = title
        self.switches = {}
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(8)
        
        if self.title:
            # 标题标签
            title_label = QLabel(self.title)
            title_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: 600;
                    background-color: transparent;
                    padding: 8px 0px;
                }
            """)
            self.layout.addWidget(title_label)
            
    def add_switch(self, switch_id: str, text: str, checked: bool = False) -> ModernSwitch:
        """添加开关"""
        switch = ModernSwitch(text, checked, self)
        self.switches[switch_id] = switch
        self.layout.addWidget(switch)
        return switch
        
    def get_switch(self, switch_id: str) -> ModernSwitch:
        """获取开关"""
        return self.switches.get(switch_id)
        
    def set_switch_checked(self, switch_id: str, checked: bool):
        """设置开关状态"""
        if switch_id in self.switches:
            self.switches[switch_id].set_checked(checked)
            
    def is_switch_checked(self, switch_id: str) -> bool:
        """获取开关状态"""
        if switch_id in self.switches:
            return self.switches[switch_id].is_checked()
        return False
        
    def get_all_states(self) -> dict:
        """获取所有开关状态"""
        return {switch_id: switch.is_checked() for switch_id, switch in self.switches.items()}
