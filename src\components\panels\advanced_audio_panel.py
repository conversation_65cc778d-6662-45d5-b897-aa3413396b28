"""
高级音频参数面板模块 - 实现压缩器、合唱和失真等高级音频效果的设置
"""

from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QGridLayout, QFrame,
    QCheckBox
)

from src.utils.style import COLORS
from src.components.slider import LabeledSlider
from src.components.collapsible_panel import CollapsiblePanel


class AdvancedAudioPanel(CollapsiblePanel):
    """高级音频参数面板，通过可折叠面板实现"""
    
    # 当设置改变时发出的信号
    enabledChanged = Signal(bool)
    compThresholdChanged = Signal(int)
    compRatioChanged = Signal(float)
    chorusRateChanged = Signal(float)
    chorusDepthChanged = Signal(float)
    distDriveChanged = Signal(int)
    
    def __init__(self, parent=None):
        # 确保先调用父类的初始化方法
        super().__init__("高级音频参数", parent)
        
        # 然后设置内容
        self._create_content()
        
    def _create_content(self):
        """创建面板内容"""
        # 创建内容容器
        content_widget = QFrame()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(16)
        
        # 启用高级参数选项
        self.enable_checkbox = QCheckBox("启用高级参数")
        self.enable_checkbox.toggled.connect(self.enabledChanged)
        self.enable_checkbox.toggled.connect(self._update_controls_state)
        content_layout.addWidget(self.enable_checkbox)
        
        # 压缩器设置
        comp_frame = QFrame()
        comp_layout = QVBoxLayout(comp_frame)
        comp_layout.setContentsMargins(0, 8, 0, 16)
        comp_layout.setSpacing(8)
        
        comp_title = QLabel("压缩器 (Compressor)")
        comp_title.setStyleSheet(f"color: {COLORS['text_primary']}; font-weight: 600;")
        comp_layout.addWidget(comp_title)
        
        comp_grid = QGridLayout()
        comp_grid.setColumnStretch(0, 1)
        comp_grid.setColumnStretch(1, 1)
        comp_grid.setHorizontalSpacing(16)
        comp_grid.setVerticalSpacing(16)
        
        # 阈值
        self.comp_threshold_slider = LabeledSlider(
            "阈值 (dB):", -60, 0, -20
        )
        self.comp_threshold_slider.valueChanged.connect(self.compThresholdChanged)
        comp_grid.addWidget(self.comp_threshold_slider, 0, 0)
        
        # 比率
        self.comp_ratio_slider = LabeledSlider(
            "比率:", 10, 200, 40  # 实际值为1.0-20.0，显示时除以10
        )
        self.comp_ratio_slider.valueChanged.connect(
            lambda v: self.compRatioChanged.emit(v / 10.0)
        )
        comp_grid.addWidget(self.comp_ratio_slider, 0, 1)
        
        comp_layout.addLayout(comp_grid)
        content_layout.addWidget(comp_frame)
        
        # 合唱效果设置
        chorus_frame = QFrame()
        chorus_layout = QVBoxLayout(chorus_frame)
        chorus_layout.setContentsMargins(0, 0, 0, 16)
        chorus_layout.setSpacing(8)
        
        chorus_title = QLabel("合唱 (Chorus)")
        chorus_title.setStyleSheet(f"color: {COLORS['text_primary']}; font-weight: 600;")
        chorus_layout.addWidget(chorus_title)
        
        chorus_grid = QGridLayout()
        chorus_grid.setColumnStretch(0, 1)
        chorus_grid.setColumnStretch(1, 1)
        chorus_grid.setHorizontalSpacing(16)
        chorus_grid.setVerticalSpacing(16)
        
        # 速率
        self.chorus_rate_slider = LabeledSlider(
            "速率 (Hz):", 1, 100, 10  # 实际值为0.1-10.0，显示时除以10
        )
        self.chorus_rate_slider.valueChanged.connect(
            lambda v: self.chorusRateChanged.emit(v / 10.0)
        )
        chorus_grid.addWidget(self.chorus_rate_slider, 0, 0)
        
        # 深度
        self.chorus_depth_slider = LabeledSlider(
            "深度:", 0, 100, 25
        )
        self.chorus_depth_slider.valueChanged.connect(
            lambda v: self.chorusDepthChanged.emit(v / 100.0)
        )
        chorus_grid.addWidget(self.chorus_depth_slider, 0, 1)
        
        chorus_layout.addLayout(chorus_grid)
        content_layout.addWidget(chorus_frame)
        
        # 失真效果设置
        dist_frame = QFrame()
        dist_layout = QVBoxLayout(dist_frame)
        dist_layout.setContentsMargins(0, 0, 0, 0)
        dist_layout.setSpacing(8)
        
        dist_title = QLabel("失真 (Distortion)")
        dist_title.setStyleSheet(f"color: {COLORS['text_primary']}; font-weight: 600;")
        dist_layout.addWidget(dist_title)
        
        # 驱动
        self.dist_drive_slider = LabeledSlider(
            "驱动 (dB):", 0, 50, 25
        )
        self.dist_drive_slider.valueChanged.connect(self.distDriveChanged)
        dist_layout.addWidget(self.dist_drive_slider)
        
        content_layout.addWidget(dist_frame)
        
        # 添加内容到可折叠面板
        self.add_widget(content_widget)
        
        # 初始状态下禁用控件
        self._update_controls_state(False)
        
    def _update_controls_state(self, enabled):
        """根据启用状态更新控件"""
        self.comp_threshold_slider.setEnabled(enabled)
        self.comp_ratio_slider.setEnabled(enabled)
        self.chorus_rate_slider.setEnabled(enabled)
        self.chorus_depth_slider.setEnabled(enabled)
        self.dist_drive_slider.setEnabled(enabled)
        
    def get_settings(self):
        """获取当前高级音频参数设置"""
        return {
            "enabled": self.enable_checkbox.isChecked(),
            "comp_threshold": self.comp_threshold_slider.value(),
            "comp_ratio": self.comp_ratio_slider.value() / 10.0,
            "chorus_rate": self.chorus_rate_slider.value() / 10.0,
            "chorus_depth": self.chorus_depth_slider.value() / 100.0,
            "dist_drive": self.dist_drive_slider.value()
        } 