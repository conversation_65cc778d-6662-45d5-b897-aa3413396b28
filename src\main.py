"""
应用入口模块 - 应用的主入口点
"""

import sys
import os
import subprocess
import importlib.util
import shutil

# 添加项目根目录到python路径中
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtCore import QDir
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtGui import QIcon, QPixmap

from src.app import MainWindow
from src.utils.style import COLORS


def check_dependencies():
    """检查并安装必要的依赖项"""
    missing_packages = []
    
    # 检查PySide6
    if importlib.util.find_spec("PySide6") is None:
        missing_packages.append("PySide6>=6.0.0")
    
    # 检查pydub
    if importlib.util.find_spec("pydub") is None:
        missing_packages.append("pydub>=0.25.1")
    
    # 检查numpy
    if importlib.util.find_spec("numpy") is None:
        missing_packages.append("numpy>=1.20.0")
    
    # 如果有缺失的包，尝试安装
    if missing_packages:
        print("缺少以下依赖项，正在尝试安装...")
        for package in missing_packages:
            print(f"- {package}")
        
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing_packages)
            print("依赖项安装完成！")
        except subprocess.CalledProcessError as e:
            print(f"依赖项安装失败: {e}")
            print("请手动安装依赖项:")
            for package in missing_packages:
                print(f"pip install {package}")
            return False
    
    # 检查ffmpeg是否可用
    try:
        # 检查ffmpeg是否在系统PATH中或项目目录下
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        ffmpeg_project_path = os.path.join(project_root, "ffmpeg", "bin", "ffmpeg.exe")
        
        # 首先检查项目目录下的ffmpeg
        if os.path.exists(ffmpeg_project_path):
            print(f"找到项目目录中的ffmpeg: {ffmpeg_project_path}")
            # 将ffmpeg目录添加到环境变量PATH中
            os.environ["PATH"] = os.path.join(project_root, "ffmpeg", "bin") + os.pathsep + os.environ["PATH"]
            return True
        
        # 如果项目目录下没有ffmpeg，则检查系统PATH
        ffmpeg_path = shutil.which('ffmpeg')
        if ffmpeg_path is None:
            print("警告：无法找到ffmpeg。波形显示和音频功能可能不可用。")
            QMessageBox.warning(
                None, 
                "缺少FFmpeg", 
                "系统中未检测到FFmpeg。\n\n音频波形显示和部分音频功能可能不可用。\n\n"
                "请安装FFmpeg后重试，或将ffmpeg放置在项目根目录下的ffmpeg/bin/目录中。"
            )
            return True
    except Exception as e:
        print(f"ffmpeg检查失败: {e}")
        return True
        
    return True


def create_temp_icons():
    """
    临时创建应用所需的图标资源文件
    在实际应用中，这些应该作为实际图像文件存放在resources目录中
    """
    resources_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "src", "resources")
    
    # 确保目录存在
    os.makedirs(resources_dir, exist_ok=True)
    
    # 创建下拉箭头图标
    arrow_icon = QPixmap(12, 12)
    arrow_icon.fill(COLORS["text_secondary"])
    arrow_icon_path = os.path.join(resources_dir, "down_arrow.png")
    arrow_icon.save(arrow_icon_path)
    
    # 创建复选框图标
    check_icon = QPixmap(12, 12)
    check_icon.fill(COLORS["text_primary"])
    check_icon_path = os.path.join(resources_dir, "checkmark.png")
    check_icon.save(check_icon_path)


def main():
    """应用入口函数"""
    # 检查依赖项
    if not check_dependencies():
        sys.exit(1)
    
    # 创建Qt应用实例
    app = QApplication(sys.argv)
    
    # 创建临时图标资源
    create_temp_icons()
    
    # 创建并显示主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用事件循环
    sys.exit(app.exec())


if __name__ == "__main__":
    main() 