"""
模型与音高面板模块 - 管理音色模型选择和音高调节
"""

from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, 
    QFrame, QLabel, QComboBox, QGridLayout
)

from src.utils.style import COLORS
from src.components.slider import LabeledSlider


class ModelPitchPanel(QFrame):
    """模型与音高设置面板"""
    
    # 当模型或音高设置改变时发出的信号
    modelChanged = Signal(str)
    configChanged = Signal(str)
    humanPitchChanged = Signal(int)
    instrumentalPitchChanged = Signal(int)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置面板属性
        self.setProperty("frameType", "panel")
        
        self._setup_ui()
        
    def _setup_ui(self):
        """设置面板UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(16)
        
        # 面板标题
        title = QLabel("模型与音高")
        title.setProperty("labelType", "title")
        main_layout.addWidget(title)
        
        # 网格布局，用于排列模型选择和音高调节
        grid_layout = QGridLayout()
        grid_layout.setHorizontalSpacing(16)
        grid_layout.setVerticalSpacing(16)
        
        # 音色模型选择
        voice_model_label = QLabel("音色模型:")
        voice_model_label.setProperty("labelType", "secondary")
        self.voice_model_combo = QComboBox()
        self.voice_model_combo.addItem("YSML.pt")
        self.voice_model_combo.currentTextChanged.connect(self.modelChanged)
        
        grid_layout.addWidget(voice_model_label, 0, 0)
        grid_layout.addWidget(self.voice_model_combo, 1, 0)
        
        # 配置文件选择
        config_label = QLabel("配置文件:")
        config_label.setProperty("labelType", "secondary")
        self.config_combo = QComboBox()
        self.config_combo.addItem("YSML.yaml")
        self.config_combo.currentTextChanged.connect(self.configChanged)
        
        grid_layout.addWidget(config_label, 0, 1)
        grid_layout.addWidget(self.config_combo, 1, 1)
        
        # 人声音高调节
        self.human_pitch_slider = LabeledSlider("人声音高:", -12, 12, 0)
        self.human_pitch_slider.valueChanged.connect(self.humanPitchChanged)
        grid_layout.addWidget(self.human_pitch_slider, 2, 0)
        
        # 伴奏音高调节
        self.instrumental_pitch_slider = LabeledSlider("伴奏音高:", -12, 12, 0)
        self.instrumental_pitch_slider.valueChanged.connect(self.instrumentalPitchChanged)
        grid_layout.addWidget(self.instrumental_pitch_slider, 2, 1)
        
        main_layout.addLayout(grid_layout)
        
    def get_settings(self):
        """获取当前面板设置"""
        return {
            "voice_model": self.voice_model_combo.currentText(),
            "config_file": self.config_combo.currentText(),
            "human_pitch": self.human_pitch_slider.value(),
            "instrumental_pitch": self.instrumental_pitch_slider.value()
        } 