"""
图标组件模块 - 为应用提供基于SVG的图标渲染
定义常用的图标实现，与HTML页面中图标一致
"""

import io
from PySide6.QtCore import QSize, Qt
from PySide6.QtGui import QIcon, QPainter, QPixmap, QColor
from PySide6.QtWidgets import QPushButton, QLabel, QWidget, QVBoxLayout
from PySide6.QtSvg import QSvgRenderer  # 添加SVG渲染器支持


# SVG图标数据字典
SVG_ICONS = {
    "music": """
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 18V5L21 3V16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M6 21C7.65685 21 9 19.6569 9 18C9 16.3431 7.65685 15 6 15C4.34315 15 3 16.3431 3 18C3 19.6569 4.34315 21 6 21Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M18 19C19.6569 19 21 17.6569 21 16C21 14.3431 19.6569 13 18 13C16.3431 13 15 14.3431 15 16C15 17.6569 16.3431 19 18 19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    """,
    "api": """
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 7H4C3.44772 7 3 7.44772 3 8V16C3 16.5523 3.44772 17 4 17H20C20.5523 17 21 16.5523 21 16V8C21 7.44772 20.5523 7 20 7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M17 21H7C6.44772 21 6 20.5523 6 20V17H18V20C18 20.5523 17.5523 21 17 21Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 7V21" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M14.5 3.5L12 7L9.5 3.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    """,
    "upload": """
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M7 8L12 3L17 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M20 21H4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    """,
    "magic": """
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 9L5 6M9 5L8 2M12 7.5L15 5.5M10 13L21 21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M10.44 13.44L8.5 18.5L7 14L2.5 12.5L7.5 10.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    """,
    "arrow_down": """
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    """,
    "chevron": """
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    """,
    "folder": """
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M22 19C22 19.5304 21.7893 20.0391 21.4142 20.4142C21.0391 20.7893 20.5304 21 20 21H4C3.46957 21 2.96086 20.7893 2.58579 20.4142C2.21071 20.0391 2 19.5304 2 19V5C2 4.46957 2.21071 3.96086 2.58579 3.58579C2.96086 3.21071 3.46957 3 4 3H9L11 6H20C20.5304 6 21.0391 6.21071 21.4142 6.58579C21.7893 6.96086 22 7.46957 22 8V19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    """,
    "more": """
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M19 13C19.5523 13 20 12.5523 20 12C20 11.4477 19.5523 11 19 11C18.4477 11 18 11.4477 18 12C18 12.5523 18.4477 13 19 13Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M5 13C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    """,
    "play": """
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5 3L19 12L5 21V3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    """,
    "pause": """
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 4H6V20H10V4Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M18 4H14V20H18V4Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    """,
    "stop": """
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6 4H18V20H6V4Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    """,
    "settings": """
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M19.4 15C19.1277 15.6171 19.2583 16.3378 19.73 16.82L19.79 16.88C20.1656 17.2554 20.3766 17.7581 20.3766 18.285C20.3766 18.8119 20.1656 19.3146 19.79 19.69C19.4146 20.0656 18.9119 20.2766 18.385 20.2766C17.8581 20.2766 17.3554 20.0656 16.98 19.69L16.92 19.63C16.4378 19.1583 15.7171 19.0277 15.1 19.3C14.4999 19.5598 14.1016 20.1439 14.1 20.8V21C14.1 22.1046 13.2046 23 12.1 23C10.9954 23 10.1 22.1046 10.1 21V20.91C10.0846 20.2264 9.66181 19.6332 9.04 19.4C8.42289 19.1277 7.70221 19.2583 7.22 19.73L7.16 19.79C6.78458 20.1656 6.28192 20.3766 5.755 20.3766C5.22808 20.3766 4.72542 20.1656 4.35 19.79C3.97438 19.4146 3.76335 18.9119 3.76335 18.385C3.76335 17.8581 3.97438 17.3554 4.35 16.98L4.41 16.92C4.88166 16.4378 5.01222 15.7171 4.74 15.1C4.48018 14.4999 3.89608 14.1016 3.24 14.1H3C1.89543 14.1 1 13.2046 1 12.1C1 10.9954 1.89543 10.1 3 10.1H3.09C3.77356 10.0846 4.36681 9.66181 4.6 9.04C4.87222 8.42289 4.74166 7.70221 4.27 7.22L4.21 7.16C3.83438 6.78458 3.62335 6.28192 3.62335 5.755C3.62335 5.22808 3.83438 4.72542 4.21 4.35C4.58542 3.97438 5.08808 3.76335 5.615 3.76335C6.14192 3.76335 6.64458 3.97438 7.02 4.35L7.08 4.41C7.56221 4.88166 8.28289 5.01222 8.9 4.74H9C9.60011 4.48018 9.99843 3.89608 10 3.24V3C10 1.89543 10.8954 1 12 1C13.1046 1 14 1.89543 14 3V3.09C14.0016 3.74608 14.3999 4.33018 15 4.59C15.6171 4.86222 16.3378 4.73166 16.82 4.26L16.88 4.2C17.2554 3.82438 17.7581 3.61335 18.285 3.61335C18.8119 3.61335 19.3146 3.82438 19.69 4.2C20.0656 4.57542 20.2766 5.07808 20.2766 5.605C20.2766 6.13192 20.0656 6.63458 19.69 7.01L19.63 7.07C19.1583 7.55221 19.0277 8.27289 19.3 8.89V9C19.5598 9.60011 20.1439 9.99843 20.8 10H21C22.1046 10 23 10.8954 23 12C23 13.1046 22.1046 14 21 14H20.91C20.2439 14.0016 19.6598 14.3999 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    """
}


def create_svg_pixmap(svg_content, size=24, color="#F2F2F7"):
    """将SVG内容转换为QPixmap，以便在Qt组件中使用"""
    # 替换颜色
    colored_svg = svg_content.replace('currentColor', color)
    
    # 创建QPixmap
    pixmap = QPixmap(QSize(size, size))
    pixmap.fill(Qt.transparent)
    
    try:
        # 使用SVG渲染器
        renderer = QSvgRenderer(colored_svg.encode('utf-8'))
        
        # 检查渲染器是否有效
        if renderer.isValid():
            # 创建绘图器
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing, True)
            
            # 渲染SVG到pixmap
            renderer.render(painter)
            painter.end()
            return pixmap
    except Exception as e:
        print(f"SVG渲染错误: {e}")
    
    # 如果SVG渲染失败，使用基本图形作为后备
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.Antialiasing, True)
    
    # 根据图标类型绘制基本形状
    if 'music' in svg_content:
        painter.setPen(QColor(color))
        painter.drawEllipse(4, 4, 16, 16)
        painter.drawLine(12, 4, 12, 16)
        painter.drawLine(12, 16, 16, 16)
    elif 'api' in svg_content:
        painter.setPen(QColor(color))
        painter.drawRect(4, 6, 16, 12)
        painter.drawLine(8, 18, 16, 18)
    else:
        painter.setPen(QColor(color))
        painter.drawRect(4, 4, 16, 16)
    
    painter.end()
    return pixmap


class IconButton(QPushButton):
    """带图标的按钮组件"""
    def __init__(self, icon_name, size=24, color="#F2F2F7", parent=None):
        super().__init__(parent)
        self.setFlat(True)
        self.setCursor(Qt.PointingHandCursor)
        self.setFixedSize(QSize(size, size))
        
        # 设置图标
        if icon_name in SVG_ICONS:
            pixmap = create_svg_pixmap(SVG_ICONS[icon_name], size, color)
            self.setIcon(QIcon(pixmap))
            self.setIconSize(QSize(size, size))


class IconLabel(QWidget):
    """带图标的标签组件"""
    def __init__(self, icon_name, text="", size=24, color="#F2F2F7", parent=None):
        super().__init__(parent)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)
        
        # 图标标签
        self.icon_label = QLabel()
        if icon_name in SVG_ICONS:
            pixmap = create_svg_pixmap(SVG_ICONS[icon_name], size, color)
            self.icon_label.setPixmap(pixmap)
        layout.addWidget(self.icon_label, alignment=Qt.AlignCenter)
        
        # 文本标签
        if text:
            self.text_label = QLabel(text)
            layout.addWidget(self.text_label, alignment=Qt.AlignCenter) 