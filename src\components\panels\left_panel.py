"""
左侧面板模块 - 实现文件上传、处理模式选择和操作按钮
"""

import os
import tempfile
from PySide6.QtCore import Qt, Signal, QSize, QTimer, QFileInfo
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, 
    QFrame, QLabel, QComboBox, 
    QPushButton, QStackedWidget, QSizePolicy,
    QFileDialog
)

from src.utils.style import COLORS
from src.components.drop_zone import DropZone
from src.components.collapsible_panel import CollapsiblePanel
from src.components.icons import SVG_ICONS, create_svg_pixmap
from src.components.loading_indicator import LoadingIndicator
from src.components.audio_player import AudioPlayer

# 检查pydub是否可用
try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    print("警告: pydub未安装，音频波形分析功能将不可用。请安装pydub: pip install pydub")


class LeftPanel(QWidget):
    """左侧功能面板，包含文件上传、处理选项和操作按钮"""
    
    # 当执行翻唱操作时发出的信号
    startProcessing = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置面板属性
        self.setMinimumWidth(250)  # 设置最小宽度，确保内容可见
        
        # 设置尺寸策略，使组件能够随窗口大小变化
        self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)
        
        # 当前音频文件
        self.current_audio_file = None
        
        # 是否正在处理中
        self.is_processing = False
        
        # 模拟进度计时器
        self.processing_timer = QTimer(self)
        self.processing_timer.timeout.connect(self._update_processing)
        self.processing_progress = 0
        
        self._setup_ui()
        
    def _setup_ui(self):
        """设置面板UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(20)
        
        # 创建文件上传/音频播放区域
        self._setup_upload_panel()
        main_layout.addWidget(self.upload_stack)
        
        # 创建歌曲列表区域
        self._setup_songs_panel()
        main_layout.addWidget(self.songs_panel)

        # 创建API状态容器
        self._setup_api_status_panel()
        main_layout.addWidget(self.api_status_container)
        
    def _setup_upload_panel(self):
        """设置文件上传/音频播放区域"""
        # 创建堆叠部件，用于切换上传区和播放器
        self.upload_stack = QStackedWidget()
        self.upload_stack.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        
        # 创建上传面板容器
        self.upload_panel = QFrame()
        self.upload_panel.setProperty("frameType", "panel")
        self.upload_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        
        # 上传面板布局
        upload_layout = QVBoxLayout(self.upload_panel)
        upload_layout.setContentsMargins(16, 16, 16, 16)
        upload_layout.setSpacing(16)
        
        # 文件拖放区
        self.drop_zone = DropZone(allowed_extensions=[".mp3", ".wav", ".ogg", ".flac"])
        self.drop_zone.fileDropped.connect(self._handle_file_dropped)
        self.drop_zone.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.drop_zone.mousePressEvent = lambda event: self._browse_audio_files() if event.button() == Qt.LeftButton else None
        self.drop_zone.setCursor(Qt.PointingHandCursor)  # 设置鼠标指针为手型，提示可点击
        upload_layout.addWidget(self.drop_zone)
        
        # 处理模式和输出格式选择
        options_layout = QHBoxLayout()
        options_layout.setSpacing(8)
        
        # 处理模式选择
        mode_layout = QVBoxLayout()
        mode_label = QLabel("处理模式:")
        mode_label.setProperty("labelType", "secondary")
        self.mode_combo = QComboBox()
        self.mode_combo.addItem("完整模式")
        self.mode_combo.addItem("干声模式")
        self.mode_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        mode_layout.addWidget(mode_label)
        mode_layout.addWidget(self.mode_combo)
        options_layout.addLayout(mode_layout)
        
        # 输出格式选择
        format_layout = QVBoxLayout()
        format_label = QLabel("输出格式:")
        format_label.setProperty("labelType", "secondary")
        self.format_combo = QComboBox()
        self.format_combo.addItem("WAV")
        self.format_combo.addItem("MP3")
        self.format_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        format_layout.addWidget(format_label)
        format_layout.addWidget(self.format_combo)
        options_layout.addLayout(format_layout)
        
        upload_layout.addLayout(options_layout)
        
        # 翻唱按钮
        sing_button_layout = QVBoxLayout()
        sing_button_layout.setSpacing(8)
        
        # 创建按钮区域
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(10)
        
        # 一键翻唱按钮
        self.one_click_cover_button = QPushButton("一键翻唱")
        self.one_click_cover_button.setProperty("buttonType", "primary")
        self.one_click_cover_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # 添加图标到按钮
        magic_pixmap = create_svg_pixmap(SVG_ICONS["magic"], 20, "#FFFFFF")
        self.one_click_cover_button.setIconSize(QSize(20, 20))
        self.one_click_cover_button.setIcon(magic_pixmap)
        self.one_click_cover_button.clicked.connect(self._on_cover_button_clicked)
        button_layout.addWidget(self.one_click_cover_button, 1)  # 1为比例权重
        
        # 加载指示器
        self.loading_indicator = LoadingIndicator(color=COLORS["accent_blue"], size=22)
        self.loading_indicator.setContentsMargins(0, 0, 8, 0)
        self.loading_indicator.hide()  # 初始隐藏
        button_layout.addWidget(self.loading_indicator)
        
        sing_button_layout.addWidget(button_container)
        upload_layout.addLayout(sing_button_layout)
        
        # 创建音频播放器面板
        self.audio_player = AudioPlayer()
        
        # 在播放器下方添加返回按钮
        self.player_container = QFrame()
        self.player_container.setProperty("frameType", "panel")
        self.player_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        
        player_layout = QVBoxLayout(self.player_container)
        player_layout.setContentsMargins(0, 0, 0, 0)
        player_layout.setSpacing(4)  # 减少间距
        
        # 添加播放器
        player_layout.addWidget(self.audio_player)
        
        # 返回按钮
        back_button_layout = QHBoxLayout()
        back_button_layout.setContentsMargins(16, 4, 16, 4)  # 减少上下边距

        back_button = QPushButton("加载其他音频")
        back_button.setProperty("buttonType", "secondary")
        back_button.clicked.connect(self._switch_to_upload_panel)
        back_button_layout.addWidget(back_button)

        player_layout.addLayout(back_button_layout)

        # 一键翻唱按钮（在播放器界面中）
        cover_button_layout = QHBoxLayout()
        cover_button_layout.setContentsMargins(16, 0, 16, 8)  # 减少底部边距

        # 创建按钮区域（移除黑色背景）
        button_container = QWidget()
        button_container.setStyleSheet("background-color: transparent;")  # 设置透明背景
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(10)

        # 一键翻唱按钮（播放器版本）
        self.player_cover_button = QPushButton("一键翻唱")
        self.player_cover_button.setProperty("buttonType", "primary")
        self.player_cover_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # 确保按钮保持蓝色背景样式
        self.player_cover_button.setStyleSheet(f"""
            QPushButton[buttonType="primary"] {{
                background-color: {COLORS["accent_blue"]};
                color: white;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: 600;
                border: none;
            }}
            QPushButton[buttonType="primary"]:hover {{
                background-color: {COLORS["accent_blue_hover"]};
            }}
            QPushButton[buttonType="primary"]:disabled {{
                background-color: {COLORS["bg_dark_tertiary"]};
                color: {COLORS["text_secondary"]};
            }}
        """)

        # 添加图标到按钮
        magic_pixmap = create_svg_pixmap(SVG_ICONS["magic"], 20, "#FFFFFF")
        self.player_cover_button.setIconSize(QSize(20, 20))
        self.player_cover_button.setIcon(magic_pixmap)
        self.player_cover_button.clicked.connect(self._on_cover_button_clicked)
        self.player_cover_button.setEnabled(False)  # 初始状态为禁用，等待API启动
        button_layout.addWidget(self.player_cover_button, 1)  # 1为比例权重

        # 加载指示器（播放器版本）
        self.player_loading_indicator = LoadingIndicator(color=COLORS["accent_blue"], size=22)
        self.player_loading_indicator.setContentsMargins(0, 0, 8, 0)
        self.player_loading_indicator.hide()  # 初始隐藏
        button_layout.addWidget(self.player_loading_indicator)

        cover_button_layout.addWidget(button_container)
        player_layout.addLayout(cover_button_layout)
        
        # 添加面板到堆叠部件
        self.upload_stack.addWidget(self.upload_panel)
        self.upload_stack.addWidget(self.player_container)
        
        # 默认显示上传面板
        self.upload_stack.setCurrentIndex(0)
        
    def _setup_songs_panel(self):
        """设置歌曲列表区域"""
        # 创建可折叠面板（启用"点击关闭"功能）
        self.songs_panel = CollapsiblePanel("歌曲列表", enable_close_text=True)
        self.songs_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 创建歌曲列表容器
        songs_container = QFrame()
        songs_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        songs_layout = QVBoxLayout(songs_container)
        songs_layout.setContentsMargins(0, 0, 0, 0)
        songs_layout.setSpacing(8)
        songs_layout.setAlignment(Qt.AlignHCenter)  # 设置水平居中对齐
        
        # 添加示例歌曲项目
        sample_songs = [
            "晴天 - 周杰伦.wav", 
            "富士山下 - 陈奕迅.wav", 
            "像我这样的人 - 毛不易.wav",
            "爱你 - 王心凌.wav", 
            "稻香 - 周杰伦.wav"
        ]
        
        for song in sample_songs:
            song_item = self._create_song_item(song)
            songs_layout.addWidget(song_item)
        
        # 将歌曲列表添加到折叠面板
        self.songs_panel.add_widget(songs_container)

    def _setup_api_status_panel(self):
        """设置API状态面板"""
        # 创建API状态容器
        self.api_status_container = QFrame()
        self.api_status_container.setProperty("frameType", "panel")
        self.api_status_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # 设置容器高度为文字的2倍高度
        font_metrics = self.api_status_container.fontMetrics()
        text_height = font_metrics.height()
        self.api_status_container.setFixedHeight(text_height * 2 + 16)  # 16为padding

        # API状态布局
        status_layout = QVBoxLayout(self.api_status_container)
        status_layout.setContentsMargins(12, 8, 12, 8)
        status_layout.setSpacing(4)
        status_layout.setAlignment(Qt.AlignCenter)

        # API状态标签
        self.api_status_label = QLabel("API服务未启动 ✗")
        self.api_status_label.setAlignment(Qt.AlignCenter)
        self.api_status_label.setStyleSheet("""
            QLabel {
                color: #FF5252;
                font-weight: bold;
                font-size: 13px;
            }
        """)
        status_layout.addWidget(self.api_status_label)
        
    def _create_song_item(self, song_name):
        """创建歌曲项目"""
        item = QFrame()
        item.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        item.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['bg_dark_tertiary']};
                border-radius: 8px;
                padding: 8px 12px;
                width: 100%;
            }}
        """)
        
        layout = QHBoxLayout(item)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)  # 减小间距，确保更紧凑
        
        # 播放图标
        play_icon = QLabel()
        play_pixmap = create_svg_pixmap(SVG_ICONS["music"], 16, "#4CAF50")
        play_icon.setPixmap(play_pixmap)
        play_icon.setFixedSize(16, 16)  # 固定图标大小
        layout.addWidget(play_icon)
        
        # 歌曲名称
        song_label = QLabel(song_name)
        song_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        song_label.setStyleSheet(f"""
            color: {COLORS['text_primary']}; 
            font-weight: 500;
        """)
        # 添加省略模式，确保文本过长时显示省略号
        song_label.setTextFormat(Qt.PlainText)
        song_label.setWordWrap(False)
        song_label.setTextInteractionFlags(Qt.NoTextInteraction)
        song_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)  # 左对齐，垂直居中
        song_label.setMinimumWidth(10)  # 确保有最小宽度
        
        # 设置省略模式
        metrics = song_label.fontMetrics()
        elidedText = metrics.elidedText(song_name, Qt.ElideRight, item.width() - 80)  # 80是一个估计值，考虑其他组件占用的空间
        song_label.setText(elidedText)
        
        layout.addWidget(song_label, 1)  # 1表示可伸缩
        
        # 操作按钮容器
        buttons_widget = QWidget()
        buttons_widget.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        buttons_layout = QHBoxLayout(buttons_widget)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(4)
        
        # 打开文件夹按钮
        folder_button = QPushButton()
        folder_button.setProperty("buttonType", "round")
        folder_button.setFixedSize(QSize(24, 24))  # 减小按钮尺寸
        folder_button.setCursor(Qt.PointingHandCursor)
        folder_pixmap = create_svg_pixmap(SVG_ICONS["folder"], 14, COLORS["text_secondary"])
        folder_button.setIcon(folder_pixmap)
        buttons_layout.addWidget(folder_button)
        
        # 更多操作按钮
        more_button = QPushButton()
        more_button.setProperty("buttonType", "round")
        more_button.setFixedSize(QSize(24, 24))  # 减小按钮尺寸
        more_button.setCursor(Qt.PointingHandCursor)
        more_pixmap = create_svg_pixmap(SVG_ICONS["more"], 14, COLORS["text_secondary"])
        more_button.setIcon(more_pixmap)
        buttons_layout.addWidget(more_button)
        
        layout.addWidget(buttons_widget)
        
        # 添加尺寸变化事件处理，确保文本自适应宽度
        item.resizeEvent = lambda event, label=song_label, name=song_name, item=item: self._resize_song_item(event, label, name, item)
        
        return item
        
    def _resize_song_item(self, event, label, original_name, item):
        """处理歌曲项尺寸变化，动态调整文本显示"""
        # 重新计算省略文本
        metrics = label.fontMetrics()
        available_width = item.width() - 80  # 保留空间给图标和按钮
        elidedText = metrics.elidedText(original_name, Qt.ElideRight, available_width)
        label.setText(elidedText)
        
    def _browse_audio_files(self):
        """打开文件选择对话框选择音频文件"""
        file_dialog = QFileDialog(self)
        file_dialog.setWindowTitle("选择音频文件")
        file_dialog.setFileMode(QFileDialog.ExistingFile)
        file_dialog.setNameFilter("音频文件 (*.mp3 *.wav *.ogg *.flac)")
        
        if file_dialog.exec():
            file_paths = file_dialog.selectedFiles()
            if file_paths:
                self._handle_file_dropped(file_paths)
    
    def _handle_file_dropped(self, file_paths):
        """处理文件被拖放或选择到区域"""
        if not file_paths:
            return
            
        file_path = file_paths[0]  # 暂时只处理第一个文件
        file_name = os.path.basename(file_path)
        
        # 设置为当前音频文件
        self.current_audio_file = file_path
        
        # 加载音频文件到播放器
        if self._load_audio_to_player(file_path):
            # 切换到播放器视图
            self.upload_stack.setCurrentIndex(1)
        else:
            # 加载失败，显示错误信息
            self.drop_zone.layout().itemAt(1).widget().setText(f"加载失败: {file_name}")
            
    def _load_audio_to_player(self, file_path):
        """加载音频文件到播放器，返回是否成功"""
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在 - {file_path}")
            return False
            
        try:
            # 加载音频文件
            result = self.audio_player.load_audio_file(file_path)
            return result
        except Exception as e:
            print(f"加载音频文件错误: {e}")
            return False
    
    def _switch_to_upload_panel(self):
        """切换回上传面板"""
        # 停止播放
        if hasattr(self, 'audio_player'):
            self.audio_player.stop()
            
        # 清除当前音频
        self.current_audio_file = None
        
        # 重置上传区显示
        self.drop_zone.layout().itemAt(1).widget().setText("选择或拖拽音频文件")
        
        # 切换到上传面板
        self.upload_stack.setCurrentIndex(0)
    
    def _on_cover_button_clicked(self):
        """一键翻唱按钮点击处理"""
        if self.is_processing:
            return  # 如果已经在处理中，则忽略
            
        # 检查是否有音频文件加载
        if not self.current_audio_file and self.upload_stack.currentIndex() == 0:
            # 在上传面板但无文件，提示用户选择文件
            self.drop_zone.layout().itemAt(1).widget().setText("请先选择或拖拽音频文件")
            return
            
        self.is_processing = True
        
        # 启动加载指示器（两个版本都启动）
        self.loading_indicator.show()
        self.loading_indicator.start()
        if hasattr(self, 'player_loading_indicator'):
            self.player_loading_indicator.show()
            self.player_loading_indicator.start()
        
        # 启动模拟进度计时器
        self.processing_progress = 0
        self.processing_timer.start(100)  # 每100毫秒更新一次
        
        # 发出开始处理信号
        self.startProcessing.emit()
    
    def _update_processing(self):
        """更新处理进度"""
        self.processing_progress += 1

        # 模拟处理完成
        if self.processing_progress >= 100:
            self.processing_timer.stop()
            self._complete_processing()
    
    def _complete_processing(self):
        """完成处理"""
        # 停止加载指示器（两个版本都停止）
        self.loading_indicator.stop()
        self.loading_indicator.hide()
        if hasattr(self, 'player_loading_indicator'):
            self.player_loading_indicator.stop()
            self.player_loading_indicator.hide()

        # 重置处理状态
        self.is_processing = False

    def update_api_status(self, is_active):
        """更新API状态显示"""
        if is_active:
            self.api_status_label.setText("API服务已启动 ✓")
            self.api_status_label.setStyleSheet("""
                QLabel {
                    color: #4CAF50;
                    font-weight: bold;
                    font-size: 13px;
                }
            """)
            # 启用翻唱按钮（两个版本）
            self.one_click_cover_button.setEnabled(True)
            self.one_click_cover_button.setToolTip("一键翻唱")
            if hasattr(self, 'player_cover_button'):
                self.player_cover_button.setEnabled(True)
                self.player_cover_button.setToolTip("一键翻唱")
        else:
            self.api_status_label.setText("API服务未启动 ✗")
            self.api_status_label.setStyleSheet("""
                QLabel {
                    color: #FF5252;
                    font-weight: bold;
                    font-size: 13px;
                }
            """)
            # 禁用翻唱按钮（两个版本）
            self.one_click_cover_button.setEnabled(False)
            self.one_click_cover_button.setToolTip("请先在API管理页面启动API服务")
            if hasattr(self, 'player_cover_button'):
                self.player_cover_button.setEnabled(False)
                self.player_cover_button.setToolTip("请先在API管理页面启动API服务")
    
    def resizeEvent(self, event):
        """处理面板尺寸调整事件"""
        super().resizeEvent(event)
        
        # 如果歌曲面板已经创建并且已展开，则更新所有歌曲项目的显示
        if hasattr(self, 'songs_panel') and hasattr(self.songs_panel, 'content_widget'):
            if self.songs_panel._is_expanded:
                # 查找所有歌曲项目并触发它们的尺寸调整事件
                songs_container = self.songs_panel.content_widget
                for i in range(songs_container.layout().count()):
                    item = songs_container.layout().itemAt(i)
                    if item and item.widget():
                        # 手动触发尺寸调整事件
                        if hasattr(item.widget(), 'resizeEvent'):
                            item.widget().resizeEvent(event) 