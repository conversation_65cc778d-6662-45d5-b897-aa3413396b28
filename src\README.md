# DDSP歌声合成GUI界面

基于PySide6的DDSP歌声合成图形界面应用，提供直观的用户操作体验。

## 项目结构

```
src/
├── components/           # 自定义UI组件
│   ├── audio_player.py   # 音频播放器组件
│   ├── collapsible_panel.py  # 可折叠面板组件
│   ├── drop_zone.py      # 文件拖放区组件
│   ├── icons.py          # 图标组件
│   ├── loading_indicator.py  # 加载指示器组件
│   └── slider.py         # 自定义滑块组件
├── resources/            # 资源文件（图标等）
├── utils/
│   └── style.py          # 全局样式定义
├── app.py                # 主窗口定义
├── main.py               # 应用入口点
└── README.md             # 本文件
```

## 实现计划

该项目分为三次实现：

### 第一次实现（当前）
- 基础框架搭建
- 样式系统和颜色主题
- 自定义组件开发（滑块、折叠面板等）
- 主窗口和导航结构

### 第二次实现（已完成部分功能）
- 歌曲制作页面
  - 音频文件上传与播放
  - 波形可视化显示
- 文件拖放功能
- 模型与音高调节
- 混响与和声、高级参数配置

### 第三次实现（待完成）
- API管理页面
- 交互逻辑完善
- 动画效果
- 性能优化

## 依赖项
本项目依赖以下库：
- PySide6：提供GUI框架
- pydub：处理音频文件
- numpy：用于数值计算
- ffmpeg：用于音频处理和波形分析（系统依赖，需单独安装）

## 使用方法

1. 安装依赖项：
```
pip install -r requirements.txt
```

2. 安装FFmpeg（用于音频处理和波形分析）：
   - Windows: https://ffmpeg.org/download.html#build-windows
   - macOS: `brew install ffmpeg`
   - Linux: `sudo apt install ffmpeg`

3. 运行应用：
```
python src/main.py
```

## 当前功能

- 基础UI框架与样式系统
- 标签页导航（歌曲制作/API管理）
- 自定义UI组件库
- 音频文件上传与播放功能
  - 支持拖放或点击选择文件
  - 支持MP3、WAV、OGG、FLAC等格式
  - 波形可视化显示，区分已播放和未播放部分
  - 播放控制与进度显示

## 项目特点

- 暗色主题设计
- 流畅动画效果
- 现代UI组件
- 符合人体工程学的交互设计 