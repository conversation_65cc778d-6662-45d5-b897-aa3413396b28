"""
文件拖放区组件模块 - 提供可拖放文件的区域控件
用于实现歌曲制作页面中的音频文件上传功能
"""

from PySide6.QtCore import Qt, QMimeData, Signal
from PySide6.QtGui import QDragEnterEvent, QDropEvent
from PySide6.QtWidgets import QFrame, QVBoxLayout, QLabel

from src.utils.style import DROP_ZONE_STYLE
from src.components.icons import SVG_ICONS, create_svg_pixmap


class DropZone(QFrame):
    """文件拖放区组件"""
    
    # 当文件被拖放到区域时发出的信号，携带文件路径列表
    fileDropped = Signal(list)
    
    def __init__(self, allowed_extensions=None, parent=None):
        super().__init__(parent)
        
        # 设置接受的文件扩展名，默认接受音频文件
        self.allowed_extensions = allowed_extensions or [".mp3", ".wav", ".ogg", ".flac"]
        
        # 设置组件属性
        self.setAcceptDrops(True)  # 允许拖放
        self.setObjectName("dropZone")
        self.setStyleSheet(DROP_ZONE_STYLE)
        self.setMinimumHeight(200)
        
        self._setup_ui()
        
    def _setup_ui(self):
        """设置组件UI"""
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(12)
        
        # 上传图标
        icon_label = QLabel()
        upload_pixmap = create_svg_pixmap(SVG_ICONS["upload"], 48, "#8A8A8E")
        icon_label.setPixmap(upload_pixmap)
        layout.addWidget(icon_label, alignment=Qt.AlignCenter)
        
        # 标题文本
        title_label = QLabel("选择或拖拽音频文件")
        title_label.setStyleSheet("color: #F2F2F7; font-size: 16px; font-weight: 600;")
        layout.addWidget(title_label, alignment=Qt.AlignCenter)
        
        # 描述文本
        desc_label = QLabel(f"支持的格式：{', '.join(self.allowed_extensions)}")
        desc_label.setStyleSheet("color: #8A8A8E; font-size: 14px;")
        layout.addWidget(desc_label, alignment=Qt.AlignCenter)
        
    def dragEnterEvent(self, event: QDragEnterEvent):
        """处理拖拽进入事件"""
        mime_data = event.mimeData()
        
        # 检查是否包含URL(文件)
        if mime_data.hasUrls():
            # 检查文件扩展名是否在允许列表中
            for url in mime_data.urls():
                file_path = url.toLocalFile()
                file_extension = "." + file_path.split(".")[-1].lower()
                
                if file_extension in self.allowed_extensions:
                    event.acceptProposedAction()
                    return
                    
        # 不接受不符合条件的拖拽
        event.ignore()
        
    def dropEvent(self, event: QDropEvent):
        """处理拖放事件"""
        mime_data = event.mimeData()
        
        if mime_data.hasUrls():
            # 提取符合条件的文件路径
            valid_file_paths = []
            
            for url in mime_data.urls():
                file_path = url.toLocalFile()
                file_extension = "." + file_path.split(".")[-1].lower()
                
                if file_extension in self.allowed_extensions:
                    valid_file_paths.append(file_path)
                    
            # 如果有有效文件，发出信号
            if valid_file_paths:
                self.fileDropped.emit(valid_file_paths)
                
            event.acceptProposedAction()
        
    def set_allowed_extensions(self, extensions):
        """设置允许的文件扩展名"""
        self.allowed_extensions = extensions 