"""
高级参数设置面板模块 - 合并推理参数配置和高级音频参数
"""

from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QGridLayout, QFrame,
    QCheckBox, QComboBox, QSpinBox
)

from src.utils.style import COLORS
from src.components.slider import LabeledSlider
from src.components.collapsible_panel import CollapsiblePanel


class AdvancedSettingsPanel(CollapsiblePanel):
    """高级参数设置面板，通过可折叠面板实现"""
    
    # 推理参数信号
    f0ExtractorChanged = Signal(str)
    pitchShiftChanged = Signal(int)
    samplingStepsChanged = Signal(int)
    samplerChanged = Signal(str)
    deviceChanged = Signal(str)
    
    # 高级音频参数信号
    enabledChanged = Signal(bool)
    compThresholdChanged = Signal(int)
    compRatioChanged = Signal(float)
    chorusRateChanged = Signal(float)
    chorusDepthChanged = Signal(float)
    distDriveChanged = Signal(int)
    
    def __init__(self, parent=None):
        # 确保先调用父类的初始化方法
        super().__init__("高级参数设置", parent)
        
        # 然后设置内容
        self._create_content()
        
    def _create_content(self):
        """创建面板内容"""
        # 创建内容容器
        content_widget = QFrame()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(24)
        
        # 添加推理参数部分
        content_layout.addWidget(self._create_inference_section())
        
        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet(f"background-color: {COLORS['border_color']}; max-height: 1px;")
        content_layout.addWidget(separator)
        
        # 添加高级音频参数部分
        content_layout.addWidget(self._create_audio_section())
        
        # 添加内容到可折叠面板
        self.add_widget(content_widget)
        
    def _create_inference_section(self):
        """创建推理参数部分"""
        inference_frame = QFrame()
        inference_layout = QGridLayout(inference_frame)
        inference_layout.setContentsMargins(0, 0, 0, 0)
        inference_layout.setHorizontalSpacing(16)
        inference_layout.setVerticalSpacing(16)
        inference_layout.setColumnStretch(0, 1)
        inference_layout.setColumnStretch(1, 1)
        
        # 添加推理参数标题
        inference_title = QLabel("推理参数")
        inference_title.setStyleSheet(f"color: {COLORS['text_primary']}; font-weight: 600; font-size: 15px;")
        inference_layout.addWidget(inference_title, 0, 0, 1, 2)
        
        # F0提取器
        f0_extractor_label = QLabel("F0提取器:")
        f0_extractor_label.setProperty("labelType", "secondary")
        self.f0_extractor_combo = QComboBox()
        self.f0_extractor_combo.addItems([
            "rmvpe (默认)", "parselmouth", "dio", "harvest", "crepe", "fcpe"
        ])
        self.f0_extractor_combo.currentTextChanged.connect(self.f0ExtractorChanged)
        
        inference_layout.addWidget(f0_extractor_label, 1, 0)
        inference_layout.addWidget(self.f0_extractor_combo, 2, 0)
        
        # 共振峰偏移
        self.pitch_shift_slider = LabeledSlider("共振峰偏移:", -6, 6, 0)
        self.pitch_shift_slider.valueChanged.connect(self.pitchShiftChanged)
        inference_layout.addWidget(self.pitch_shift_slider, 1, 1, 2, 1)
        
        # 采样步数
        sampling_steps_label = QLabel("采样步数:")
        sampling_steps_label.setProperty("labelType", "secondary")
        self.sampling_steps_spin = QSpinBox()
        self.sampling_steps_spin.setRange(10, 500)
        self.sampling_steps_spin.setValue(50)
        self.sampling_steps_spin.valueChanged.connect(self.samplingStepsChanged)
        
        inference_layout.addWidget(sampling_steps_label, 3, 0)
        inference_layout.addWidget(self.sampling_steps_spin, 4, 0)
        
        # 采样器
        sampler_label = QLabel("采样器:")
        sampler_label.setProperty("labelType", "secondary")
        self.sampler_combo = QComboBox()
        self.sampler_combo.addItems(["euler", "rk4"])
        self.sampler_combo.currentTextChanged.connect(self.samplerChanged)
        
        inference_layout.addWidget(sampler_label, 3, 1)
        inference_layout.addWidget(self.sampler_combo, 4, 1)
        
        # 设备选择
        device_label = QLabel("设备选择:")
        device_label.setProperty("labelType", "secondary")
        self.device_combo = QComboBox()
        self.device_combo.addItems(["CUDA (默认)", "CPU"])
        self.device_combo.currentTextChanged.connect(self.deviceChanged)
        
        inference_layout.addWidget(device_label, 5, 0)
        inference_layout.addWidget(self.device_combo, 6, 0, 1, 2)
        
        return inference_frame
    
    def _create_audio_section(self):
        """创建高级音频参数部分"""
        audio_frame = QFrame()
        audio_layout = QVBoxLayout(audio_frame)
        audio_layout.setContentsMargins(0, 0, 0, 0)
        audio_layout.setSpacing(16)
        
        # 添加高级音频参数标题
        audio_title = QLabel("高级音频参数")
        audio_title.setStyleSheet(f"color: {COLORS['text_primary']}; font-weight: 600; font-size: 15px;")
        audio_layout.addWidget(audio_title)
        
        # 启用高级参数选项
        self.enable_checkbox = QCheckBox("启用高级参数")
        self.enable_checkbox.toggled.connect(self.enabledChanged)
        self.enable_checkbox.toggled.connect(self._update_controls_state)
        audio_layout.addWidget(self.enable_checkbox)
        
        # 压缩器设置
        comp_frame = QFrame()
        comp_layout = QVBoxLayout(comp_frame)
        comp_layout.setContentsMargins(0, 8, 0, 16)
        comp_layout.setSpacing(8)
        
        comp_title = QLabel("压缩器 (Compressor)")
        comp_title.setStyleSheet(f"color: {COLORS['text_primary']}; font-weight: 600;")
        comp_layout.addWidget(comp_title)
        
        comp_grid = QGridLayout()
        comp_grid.setColumnStretch(0, 1)
        comp_grid.setColumnStretch(1, 1)
        comp_grid.setHorizontalSpacing(16)
        comp_grid.setVerticalSpacing(16)
        
        # 阈值
        self.comp_threshold_slider = LabeledSlider(
            "阈值 (dB):", -60, 0, -20
        )
        self.comp_threshold_slider.valueChanged.connect(self.compThresholdChanged)
        comp_grid.addWidget(self.comp_threshold_slider, 0, 0)
        
        # 比率
        self.comp_ratio_slider = LabeledSlider(
            "比率:", 10, 200, 40  # 实际值为1.0-20.0，显示时除以10
        )
        self.comp_ratio_slider.valueChanged.connect(
            lambda v: self.compRatioChanged.emit(v / 10.0)
        )
        comp_grid.addWidget(self.comp_ratio_slider, 0, 1)
        
        comp_layout.addLayout(comp_grid)
        audio_layout.addWidget(comp_frame)
        
        # 合唱效果设置
        chorus_frame = QFrame()
        chorus_layout = QVBoxLayout(chorus_frame)
        chorus_layout.setContentsMargins(0, 0, 0, 16)
        chorus_layout.setSpacing(8)
        
        chorus_title = QLabel("合唱 (Chorus)")
        chorus_title.setStyleSheet(f"color: {COLORS['text_primary']}; font-weight: 600;")
        chorus_layout.addWidget(chorus_title)
        
        chorus_grid = QGridLayout()
        chorus_grid.setColumnStretch(0, 1)
        chorus_grid.setColumnStretch(1, 1)
        chorus_grid.setHorizontalSpacing(16)
        chorus_grid.setVerticalSpacing(16)
        
        # 速率
        self.chorus_rate_slider = LabeledSlider(
            "速率 (Hz):", 1, 100, 10  # 实际值为0.1-10.0，显示时除以10
        )
        self.chorus_rate_slider.valueChanged.connect(
            lambda v: self.chorusRateChanged.emit(v / 10.0)
        )
        chorus_grid.addWidget(self.chorus_rate_slider, 0, 0)
        
        # 深度
        self.chorus_depth_slider = LabeledSlider(
            "深度:", 0, 100, 25
        )
        self.chorus_depth_slider.valueChanged.connect(
            lambda v: self.chorusDepthChanged.emit(v / 100.0)
        )
        chorus_grid.addWidget(self.chorus_depth_slider, 0, 1)
        
        chorus_layout.addLayout(chorus_grid)
        audio_layout.addWidget(chorus_frame)
        
        # 失真效果设置
        dist_frame = QFrame()
        dist_layout = QVBoxLayout(dist_frame)
        dist_layout.setContentsMargins(0, 0, 0, 0)
        dist_layout.setSpacing(8)
        
        dist_title = QLabel("失真 (Distortion)")
        dist_title.setStyleSheet(f"color: {COLORS['text_primary']}; font-weight: 600;")
        dist_layout.addWidget(dist_title)
        
        # 驱动
        self.dist_drive_slider = LabeledSlider(
            "驱动 (dB):", 0, 50, 25
        )
        self.dist_drive_slider.valueChanged.connect(self.distDriveChanged)
        dist_layout.addWidget(self.dist_drive_slider)
        
        audio_layout.addWidget(dist_frame)
        
        # 初始状态下禁用控件
        self._update_controls_state(False)
        
        return audio_frame
        
    def _update_controls_state(self, enabled):
        """根据启用状态更新控件"""
        self.comp_threshold_slider.setEnabled(enabled)
        self.comp_ratio_slider.setEnabled(enabled)
        self.chorus_rate_slider.setEnabled(enabled)
        self.chorus_depth_slider.setEnabled(enabled)
        self.dist_drive_slider.setEnabled(enabled)
        
    def get_inference_settings(self):
        """获取当前推理参数设置"""
        return {
            "f0_extractor": self.f0_extractor_combo.currentText().split(" ")[0],  # 移除"(默认)"部分
            "pitch_shift": self.pitch_shift_slider.value(),
            "sampling_steps": self.sampling_steps_spin.value(),
            "sampler": self.sampler_combo.currentText(),
            "device": "cuda" if "CUDA" in self.device_combo.currentText() else "cpu"
        }
    
    def get_audio_settings(self):
        """获取当前高级音频参数设置"""
        return {
            "enabled": self.enable_checkbox.isChecked(),
            "comp_threshold": self.comp_threshold_slider.value(),
            "comp_ratio": self.comp_ratio_slider.value() / 10.0,
            "chorus_rate": self.chorus_rate_slider.value() / 10.0,
            "chorus_depth": self.chorus_depth_slider.value() / 100.0,
            "dist_drive": self.dist_drive_slider.value()
        } 