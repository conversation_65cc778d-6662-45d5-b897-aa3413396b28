"""
推理参数配置面板模块 - 设置模型推理的高级参数
"""

from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QGridLayout, QFrame,
    QComboBox, QSpinBox
)

from src.utils.style import COLORS
from src.components.slider import LabeledSlider
from src.components.collapsible_panel import CollapsiblePanel


class InferencePanel(CollapsiblePanel):
    """推理参数配置面板，通过可折叠面板实现"""
    
    # 当设置改变时发出的信号
    f0ExtractorChanged = Signal(str)
    pitchShiftChanged = Signal(int)
    samplingStepsChanged = Signal(int)
    samplerChanged = Signal(str)
    deviceChanged = Signal(str)
    
    def __init__(self, parent=None):
        # 确保先调用父类的初始化方法
        super().__init__("推理参数配置", parent)
        
        # 然后设置内容
        self._create_content()
        
    def _create_content(self):
        """创建面板内容"""
        # 创建内容容器
        content_widget = QFrame()
        content_layout = QGridLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setHorizontalSpacing(16)
        content_layout.setVerticalSpacing(16)
        content_layout.setColumnStretch(0, 1)
        content_layout.setColumnStretch(1, 1)
        
        # F0提取器
        f0_extractor_label = QLabel("F0提取器:")
        f0_extractor_label.setProperty("labelType", "secondary")
        self.f0_extractor_combo = QComboBox()
        self.f0_extractor_combo.addItems([
            "rmvpe (默认)", "parselmouth", "dio", "harvest", "crepe", "fcpe"
        ])
        self.f0_extractor_combo.currentTextChanged.connect(self.f0ExtractorChanged)
        
        content_layout.addWidget(f0_extractor_label, 0, 0)
        content_layout.addWidget(self.f0_extractor_combo, 1, 0)
        
        # 共振峰偏移
        self.pitch_shift_slider = LabeledSlider("共振峰偏移:", -6, 6, 0)
        self.pitch_shift_slider.valueChanged.connect(self.pitchShiftChanged)
        content_layout.addWidget(self.pitch_shift_slider, 0, 1, 2, 1)
        
        # 采样步数
        sampling_steps_label = QLabel("采样步数:")
        sampling_steps_label.setProperty("labelType", "secondary")
        self.sampling_steps_spin = QSpinBox()
        self.sampling_steps_spin.setRange(10, 500)
        self.sampling_steps_spin.setValue(50)
        self.sampling_steps_spin.valueChanged.connect(self.samplingStepsChanged)
        
        content_layout.addWidget(sampling_steps_label, 2, 0)
        content_layout.addWidget(self.sampling_steps_spin, 3, 0)
        
        # 采样器
        sampler_label = QLabel("采样器:")
        sampler_label.setProperty("labelType", "secondary")
        self.sampler_combo = QComboBox()
        self.sampler_combo.addItems(["euler", "rk4"])
        self.sampler_combo.currentTextChanged.connect(self.samplerChanged)
        
        content_layout.addWidget(sampler_label, 2, 1)
        content_layout.addWidget(self.sampler_combo, 3, 1)
        
        # 设备选择
        device_label = QLabel("设备选择:")
        device_label.setProperty("labelType", "secondary")
        self.device_combo = QComboBox()
        self.device_combo.addItems(["CUDA (默认)", "CPU"])
        self.device_combo.currentTextChanged.connect(self.deviceChanged)
        
        content_layout.addWidget(device_label, 4, 0)
        content_layout.addWidget(self.device_combo, 5, 0, 1, 2)
        
        # 添加内容到可折叠面板
        self.add_widget(content_widget)
        
    def get_settings(self):
        """获取当前推理参数设置"""
        return {
            "f0_extractor": self.f0_extractor_combo.currentText().split(" ")[0],  # 移除"(默认)"部分
            "pitch_shift": self.pitch_shift_slider.value(),
            "sampling_steps": self.sampling_steps_spin.value(),
            "sampler": self.sampler_combo.currentText(),
            "device": "cuda" if "CUDA" in self.device_combo.currentText() else "cpu"
        } 