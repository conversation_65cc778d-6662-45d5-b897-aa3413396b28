"""
混响与和声面板模块 - 混响效果和和声设置
"""

from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QGridLayout, QFrame,
    QCheckBox
)

from src.utils.style import COLORS
from src.components.slider import LabeledSlider
from src.components.collapsible_panel import CollapsiblePanel


class ReverbPanel(CollapsiblePanel):
    """混响与和声面板，通过可折叠面板实现"""
    
    # 当混响设置改变时发出的信号
    reverbEnabledChanged = Signal(bool)
    harmonyWithInstrumentalChanged = Signal(bool)
    roomSizeChanged = Signal(float)
    dampingChanged = Signal(float)
    widthChanged = Signal(float)
    wetLevelChanged = Signal(float)
    dryLevelChanged = Signal(float)
    delaySecondsChanged = Signal(float)
    
    def __init__(self, parent=None):
        # 确保先调用父类的初始化方法
        super().__init__("混响与和声", parent)
        
        # 然后设置内容
        self._create_content()
        
        # 设置面板默认展开
        self.set_expanded(True)
        
    def _create_content(self):
        """创建面板内容"""
        # 创建内容容器
        content_widget = QFrame()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(16)
        
        # 选项复选框部分
        options_layout = QHBoxLayout()
        options_layout.setSpacing(16)
        
        # 启用混响复选框
        self.reverb_checkbox = QCheckBox("启用混响")
        self.reverb_checkbox.setChecked(True)  # 默认选中
        self.reverb_checkbox.toggled.connect(self.reverbEnabledChanged)
        options_layout.addWidget(self.reverb_checkbox)
        
        # 和声加入伴奏复选框
        self.harmony_checkbox = QCheckBox("和声加入伴奏")
        self.harmony_checkbox.toggled.connect(self.harmonyWithInstrumentalChanged)
        options_layout.addWidget(self.harmony_checkbox)
        
        content_layout.addLayout(options_layout)
        
        # 滑块设置部分
        sliders_grid = QGridLayout()
        sliders_grid.setColumnStretch(0, 1)
        sliders_grid.setColumnStretch(1, 1)
        sliders_grid.setHorizontalSpacing(16)
        sliders_grid.setVerticalSpacing(16)
        
        # 添加所有滑块控件
        # 房间大小
        self.room_size_slider = LabeledSlider(
            "房间大小:", 0, 100, 50
        )
        self.room_size_slider.valueChanged.connect(
            lambda v: self.roomSizeChanged.emit(v / 100.0)
        )
        sliders_grid.addWidget(self.room_size_slider, 0, 0)
        
        # 阻尼
        self.damping_slider = LabeledSlider(
            "阻尼:", 0, 100, 50
        )
        self.damping_slider.valueChanged.connect(
            lambda v: self.dampingChanged.emit(v / 100.0)
        )
        sliders_grid.addWidget(self.damping_slider, 0, 1)
        
        # 宽度
        self.width_slider = LabeledSlider(
            "宽度:", 0, 100, 100
        )
        self.width_slider.valueChanged.connect(
            lambda v: self.widthChanged.emit(v / 100.0)
        )
        sliders_grid.addWidget(self.width_slider, 1, 0)
        
        # 湿润度
        self.wet_level_slider = LabeledSlider(
            "湿润度:", 0, 100, 33
        )
        self.wet_level_slider.valueChanged.connect(
            lambda v: self.wetLevelChanged.emit(v / 100.0)
        )
        sliders_grid.addWidget(self.wet_level_slider, 1, 1)
        
        # 干燥度
        self.dry_level_slider = LabeledSlider(
            "干燥度:", 0, 100, 40
        )
        self.dry_level_slider.valueChanged.connect(
            lambda v: self.dryLevelChanged.emit(v / 100.0)
        )
        sliders_grid.addWidget(self.dry_level_slider, 2, 0)
        
        # 延迟
        self.delay_slider = LabeledSlider(
            "延迟:", 0, 200, 0
        )
        self.delay_slider.valueChanged.connect(
            lambda v: self.delaySecondsChanged.emit(v / 100.0)
        )
        sliders_grid.addWidget(self.delay_slider, 2, 1)
        
        content_layout.addLayout(sliders_grid)
        
        # 添加内容到可折叠面板
        self.add_widget(content_widget)
        
    def get_settings(self):
        """获取当前混响设置"""
        return {
            "reverb_enabled": self.reverb_checkbox.isChecked(),
            "harmony_with_instrumental": self.harmony_checkbox.isChecked(),
            "room_size": self.room_size_slider.value() / 100.0,
            "damping": self.damping_slider.value() / 100.0,
            "width": self.width_slider.value() / 100.0,
            "wet_level": self.wet_level_slider.value() / 100.0,
            "dry_level": self.dry_level_slider.value() / 100.0,
            "delay_seconds": self.delay_slider.value() / 100.0
        } 