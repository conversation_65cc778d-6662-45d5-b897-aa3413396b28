<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>AI翻唱 - 设计参考</title>
    <!-- Using Tailwind CSS for layout and utilities -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet"/>
    <!-- Google Material Icons for iconography -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
    <style>
        /* * Custom Stylesheet
         * This design is inspired by the provided reference image and is tailored to be
         * easily translatable to PySide6 QSS (Qt Style Sheets).
         * It avoids complex CSS properties that don't have direct equivalents in QSS,
         * focusing on colors, borders, radii, and layout.
        */

        :root {
            --bg-dark-primary: #1C1C1E;      /* App background, very dark gray */
            --bg-dark-secondary: #2C2C2E;   /* Panel/Card background */
            --bg-dark-tertiary: #3A3A3C;    /* Input fields, hover states */
            --accent-blue: #0A84FF;         /* Primary action blue */
            --accent-blue-hover: #3DA0FF;   /* Lighter blue for hover */
            --text-primary: #F2F2F7;       /* Main text color, almost white */
            --text-secondary: #8A8A8E;     /* Secondary/label text, gray */
            --border-color: #444446;       /* Subtle border color */
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--bg-dark-primary);
            color: var(--text-primary);
        }

        /* Main application container */
        .app-container {
            width: 100%;
            max-width: 1000px; /* Adjusted for target size */
            height: 720px;     /* Adjusted for target size */
            margin: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            background-color: #101012;
            border-radius: 12px;
        }
        
        /* Top Tab Navigation */
        .top-tabs {
            display: flex;
            flex-shrink: 0;
            padding: 0 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }
        .tab-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 14px 4px;
            margin: 0 12px;
            border: none;
            background-color: transparent;
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 0.95rem;
            border-bottom: 3px solid transparent;
            cursor: pointer;
            transition: color 0.2s, border-color 0.2s;
        }
        .tab-button:first-child { margin-left: 0; }
        .tab-button:hover {
            color: var(--text-primary);
        }
        .tab-button.active {
            color: var(--accent-blue);
            font-weight: 600;
            border-bottom-color: var(--accent-blue);
        }
        .tab-button .material-icons {
             font-size: 20px;
        }


        /* Main Content Area */
        .main-content {
            flex-grow: 1;
            overflow-y: auto;
            padding: 1.5rem;
        }

        /* Hides content that is not active */
        .page-content {
            display: none;
        }
        .page-content.active {
            display: block;
        }

        /* Card/Panel Style */
        .panel {
            background-color: var(--bg-dark-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
        }

        /* General UI Elements */
        .label-text {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        .input-field {
            background-color: var(--bg-dark-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 10px 12px;
            color: var(--text-primary);
            transition: border-color 0.2s;
            width: 100%;
        }
        .input-field:focus {
            outline: none;
            border-color: var(--accent-blue);
        }

        /* Buttons */
        .btn {
            color: white;
            font-weight: 600;
            padding: 12px 20px;
            border-radius: 8px;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }
        .btn-primary {
            background-color: var(--accent-blue);
        }
        .btn-primary:hover {
            background-color: var(--accent-blue-hover);
        }
        .btn-secondary {
            background-color: var(--bg-dark-tertiary);
        }
        .btn-secondary:hover {
            background-color: #4F4F52;
        }
         .btn-danger {
            background-color: #E53E3E;
        }
        .btn-danger:hover {
            background-color: #FC8181;
        }

        /* Sliders */
        input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
            width: 100%;
            height: 6px;
            background: var(--bg-dark-tertiary);
            border-radius: 3px;
            outline: none;
            cursor: pointer;
        }
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            background: var(--accent-blue);
            border-radius: 50%;
            border: 3px solid var(--bg-dark-secondary);
        }
        input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            background: var(--accent-blue);
            border-radius: 50%;
            border: 3px solid var(--bg-dark-secondary);
        }

        /* Collapsible Sections */
        .collapsible-container {
             max-height: 0;
             overflow: hidden;
             transition: max-height 0.4s ease-in-out;
        }
        .collapsible-toggle-btn {
            background: none;
            border: none;
            width: 100%;
            text-align: left;
            padding: 0;
            cursor: pointer;
        }
        .collapsible-toggle-btn .material-icons {
             transition: transform 0.3s ease;
        }

        /* Custom Checkbox */
        .custom-checkbox {
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            color: var(--text-secondary);
        }
        .custom-checkbox input {
            display: none; /* Hide original checkbox */
        }
        .custom-checkbox .checkmark {
            width: 18px;
            height: 18px;
            background-color: var(--bg-dark-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 5px;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }
         .custom-checkbox .checkmark .material-icons {
            display: none;
            font-size: 16px;
            color: white;
         }
        .custom-checkbox input:checked + .checkmark {
            background-color: var(--accent-blue);
            border-color: var(--accent-blue);
        }
        .custom-checkbox input:checked + .checkmark .material-icons {
            display: block;
        }
        
        /* Custom Scrollbar for better look */
        .main-content::-webkit-scrollbar { width: 8px; }
        .main-content::-webkit-scrollbar-track { background: transparent; }
        .main-content::-webkit-scrollbar-thumb {
          background-color: #555;
          border-radius: 4px;
        }
        .main-content::-webkit-scrollbar-thumb:hover { background-color: #777; }

    </style>
</head>
<body class="p-4">

    <div class="app-container">
        <!-- Top Tab Bar -->
        <nav class="top-tabs">
             <button id="nav-song-production" class="tab-button active" onclick="openPage('song-production')">
                <span class="material-icons">music_note</span>
                <span>歌曲制作</span>
            </button>
            <button id="nav-api-management" class="tab-button" onclick="openPage('api-management')">
                <span class="material-icons">settings_ethernet</span>
                <span>API管理</span>
            </button>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Page 1: Song Production -->
            <div id="song-production" class="page-content active">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Left Column -->
                    <div class="lg:col-span-1 space-y-6">
                        <div class="panel">
                             <div id="drop-zone" class="flex flex-col items-center justify-center p-6 border-2 border-dashed rounded-lg cursor-pointer transition-colors" style="border-color: var(--border-color);">
                                <input type="file" id="file-input" class="hidden" accept="audio/*">
                                <span class="material-icons text-6xl mb-3" style="color: var(--text-secondary);">upload_file</span>
                                <p id="drop-zone-text" class="text-center pointer-events-none" style="color: var(--text-secondary);">选择或拖拽音频文件</p>
                            </div>
                            <div class="mt-4 grid grid-cols-2 gap-4">
                                <div>
                                    <label class="label-text block" for="processing-mode">处理模式:</label>
                                    <select class="input-field w-full" id="processing-mode">
                                        <option>完整模式</option>
                                        <option>干声模式</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="label-text block" for="output-format">输出格式:</label>
                                    <select class="input-field w-full" id="output-format">
                                        <option>WAV</option>
                                        <option>MP3</option>
                                    </select>
                                </div>
                            </div>
                             <div class="mt-6">
                                 <button class="btn btn-primary w-full text-lg">
                                    <span class="material-icons mr-2">auto_awesome</span>
                                    一键翻唱
                                 </button>
                                <p class="text-center mt-3 text-sm" style="color: var(--text-secondary);">状态: <span class="font-semibold" style="color: var(--text-primary);">空闲</span></p>
                            </div>
                        </div>
                         <div class="panel">
                             <button class="collapsible-toggle-btn w-full flex justify-between items-center">
                                <h3 class="text-lg font-semibold">我的成品歌曲</h3>
                                <span class="material-icons">expand_more</span>
                             </button>
                             <div class="collapsible-container">
                                 <div id="song-list" class="space-y-2 pt-4 mt-4 border-t" style="border-color: var(--border-color);">
                                    <!-- Song items will be dynamically added here -->
                                 </div>
                             </div>
                         </div>
                    </div>

                    <!-- Right Column -->
                    <div class="lg:col-span-2 space-y-6">
                        <div class="panel">
                            <h3 class="text-lg font-semibold">模型与音高</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                                 <div><label class="label-text block" for="voice-model">音色模型:</label><select class="input-field w-full" id="voice-model"><option>YSML.pt</option></select></div>
                                 <div><label class="label-text block" for="config-file">配置文件:</label><select class="input-field w-full" id="config-file"><option>YSML.yaml</option></select></div>
                                 <div class="md:col-span-1"><label class="label-text block" for="human-pitch">人声音高:</label><div class="flex items-center space-x-3"><input class="w-full" id="human-pitch" max="12" min="-12" type="range" value="0"/><span class="value-text w-8 text-right font-mono">0</span></div></div>
                                 <div class="md:col-span-1"><label class="label-text block" for="instrumental-pitch">伴奏音高:</label><div class="flex items-center space-x-3"><input class="w-full" id="instrumental-pitch" max="12" min="-12" type="range" value="0"/><span class="value-text w-8 text-right font-mono">0</span></div></div>
                            </div>
                        </div>
                        
                        <div class="panel">
                            <button class="collapsible-toggle-btn w-full flex justify-between items-center">
                                <h3 class="text-lg font-semibold">混响与和声</h3>
                                <span class="material-icons">expand_more</span>
                            </button>
                            <div class="collapsible-container">
                                <div class="pt-4 mt-4 border-t" style="border-color: var(--border-color);">
                                    <div class="md:col-span-2 flex items-center space-x-6 mb-4">
                                        <label class="custom-checkbox"><input type="checkbox" checked><span class="checkmark"><span class="material-icons">check</span></span><span>启用混响</span></label>
                                        <label class="custom-checkbox"><input type="checkbox"><span class="checkmark"><span class="material-icons">check</span></span><span>和声加入伴奏</span></label>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                                        <div><label class="label-text block" for="room-size">房间大小:</label><div class="flex items-center space-x-3"><input class="w-full" id="room-size" max="1" min="0" step="0.01" type="range" value="0.5"/><span class="value-text w-12 text-right font-mono">0.50</span></div></div>
                                        <div><label class="label-text block" for="reverb-damping">阻尼:</label><div class="flex items-center space-x-3"><input class="w-full" id="reverb-damping" max="1" min="0" step="0.01" type="range" value="0.5"/><span class="value-text w-12 text-right font-mono">0.50</span></div></div>
                                        <div><label class="label-text block" for="reverb-width">宽度:</label><div class="flex items-center space-x-3"><input class="w-full" id="reverb-width" max="1" min="0" step="0.01" type="range" value="1.0"/><span class="value-text w-12 text-right font-mono">1.00</span></div></div>
                                        <div><label class="label-text block" for="wet-level">湿润度:</label><div class="flex items-center space-x-3"><input class="w-full" id="wet-level" max="1" min="0" step="0.01" type="range" value="0.33"/><span class="value-text w-12 text-right font-mono">0.33</span></div></div>
                                        <div><label class="label-text block" for="dry-level">干燥度:</label><div class="flex items-center space-x-3"><input class="w-full" id="dry-level" max="1" min="0" step="0.01" type="range" value="0.4"/><span class="value-text w-12 text-right font-mono">0.40</span></div></div>
                                        <div><label class="label-text block" for="delay-seconds">延迟:</label><div class="flex items-center space-x-3"><input class="w-full" id="delay-seconds" max="2" min="0" step="0.01" type="range" value="0.0"/><span class="value-text w-12 text-right font-mono">0.00s</span></div></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel">
                             <button class="collapsible-toggle-btn w-full flex justify-between items-center">
                                <h3 class="text-lg font-semibold">高级音频参数</h3>
                                <span class="material-icons">expand_more</span>
                            </button>
                            <div class="collapsible-container">
                                <div class="pt-4 mt-4 border-t" style="border-color: var(--border-color);">
                                    <div class="mb-4"><label class="custom-checkbox"><input type="checkbox"><span class="checkmark"><span class="material-icons">check</span></span><span>启用高级参数</span></label></div>
                                    <div class="space-y-5">
                                        <div>
                                            <h4 class="font-semibold" style="color: var(--text-primary);">压缩器 (Compressor)</h4>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mt-2">
                                                <div><label class="label-text block" for="comp-threshold">阈值 (dB):</label><div class="flex items-center space-x-3"><input class="w-full" id="comp-threshold" max="0" min="-60" step="1" type="range" value="-20"/><span class="value-text w-10 text-right font-mono">-20</span></div></div>
                                                <div><label class="label-text block" for="comp-ratio">比率:</label><div class="flex items-center space-x-3"><input class="w-full" id="comp-ratio" max="20" min="1" step="0.1" type="range" value="4"/><span class="value-text w-10 text-right font-mono">4.0</span></div></div>
                                            </div>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold" style="color: var(--text-primary);">合唱 (Chorus)</h4>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mt-2">
                                                <div><label class="label-text block" for="chorus-rate">速率 (Hz):</label><div class="flex items-center space-x-3"><input class="w-full" id="chorus-rate" max="10" min="0.1" step="0.1" type="range" value="1.0"/><span class="value-text w-10 text-right font-mono">1.0</span></div></div>
                                                <div><label class="label-text block" for="chorus-depth">深度:</label><div class="flex items-center space-x-3"><input class="w-full" id="chorus-depth" max="1" min="0" step="0.01" type="range" value="0.25"/><span class="value-text w-10 text-right font-mono">0.25</span></div></div>
                                            </div>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold" style="color: var(--text-primary);">失真 (Distortion)</h4>
                                            <div class="mt-2"><label class="label-text block" for="dist-drive">驱动 (dB):</label><div class="flex items-center space-x-3"><input class="w-full" id="dist-drive" max="50" min="0" step="1" type="range" value="25"/><span class="value-text w-10 text-right font-mono">25</span></div></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                         <div class="panel">
                             <button class="collapsible-toggle-btn w-full flex justify-between items-center">
                                <h3 class="text-lg font-semibold">推理参数配置</h3>
                                <span class="material-icons">expand_more</span>
                            </button>
                            <div class="collapsible-container">
                                <div class="pt-4 mt-4 border-t grid grid-cols-1 md:grid-cols-2 gap-6" style="border-color: var(--border-color);">
                                    <div><label class="label-text block" for="f0-extractor">F0提取器:</label><select class="input-field w-full" id="f0-extractor"><option>rmvpe (默认)</option><option>parselmouth</option><option>dio</option><option>harvest</option><option>crepe</option><option>fcpe</option></select></div>
                                    <div><label class="label-text block" for="pitch-shift">共振峰偏移:</label><div class="flex items-center space-x-3"><input class="w-full" id="pitch-shift" max="6" min="-6" type="range" value="0"/><span class="value-text w-8 text-right font-mono">0</span></div></div>
                                    <div><label class="label-text block" for="sampling-steps">采样步数:</label><input class="input-field w-full" id="sampling-steps" type="number" value="50"/></div>
                                    <div><label class="label-text block" for="sampler">采样器:</label><select class="input-field w-full" id="sampler"><option>euler</option><option>rk4</option></select></div>
                                    <div class="md:col-span-2"><label class="label-text block" for="device-selection">设备选择:</label><select class="input-field w-full" id="device-selection"><option>CUDA (默认)</option><option>CPU</option></select></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page 2: API Management -->
            <div id="api-management" class="page-content">
                <div class="panel">
                    <h2 class="text-xl font-semibold">API 配置</h2>
                    <div class="space-y-4 my-6">
                        <div>
                            <label class="label-text block" for="api-url">API URL</label>
                            <input class="input-field w-full" id="api-url" type="text" value="http://127.0.0.1:9880"/>
                        </div>
                        <div>
                            <label class="label-text block" for="python-env">Python 环境路径</label>
                            <div class="flex items-center gap-2">
                                <input class="input-field w-full" id="python-env" type="text" placeholder="例如: C:\path\to\venv\python.exe"/>
                                <button class="btn btn-secondary whitespace-nowrap">浏览...</button>
                            </div>
                        </div>
                        <div class="flex items-center space-x-6 pt-2">
                           <label class="custom-checkbox"><input type="checkbox" checked><span class="checkmark"><span class="material-icons">check</span></span><span>随软件启动API</span></label>
                           <label class="custom-checkbox"><input type="checkbox"><span class="checkmark"><span class="material-icons">check</span></span><span>云端API</span></label>
                        </div>
                    </div>

                    <div class="space-y-4">
                       <h3 class="text-lg font-semibold">控制台输出</h3>
                       <div class="w-full h-48 bg-black rounded-lg p-3 font-mono text-sm text-gray-400 overflow-y-auto">
                           <p>&gt; API服务未启动...</p>
                       </div>
                       <div class="grid grid-cols-2 gap-4 pt-2">
                            <button class="btn btn-primary">启动 API</button>
                            <button class="btn btn-danger">停止 API</button>
                       </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // --- Page Navigation ---
        function openPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });
            // Deactivate all nav buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected page
            document.getElementById(pageId).classList.add('active');
            // Activate selected nav button
            document.getElementById('nav-' + pageId).classList.add('active');
        }

        // --- Slider Value Display Update ---
        document.querySelectorAll('input[type="range"]').forEach(slider => {
            const valueSpan = slider.parentElement.querySelector('.value-text');
            if (valueSpan) {
                const updateValue = () => {
                    let value = parseFloat(slider.value);
                    if (slider.step === '0.01') {
                        value = value.toFixed(2);
                        if (slider.id === 'delay-seconds') value += 's';
                    } else if (slider.step === '0.1') {
                        value = value.toFixed(1);
                    }
                    valueSpan.textContent = value;
                };
                updateValue();
                slider.addEventListener('input', updateValue);
            }
        });
        
        // --- Collapsible Container Logic ---
        document.querySelectorAll('.collapsible-toggle-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const container = btn.nextElementSibling;
                const arrowIcon = btn.querySelector('.material-icons');
                if (container.style.maxHeight && container.style.maxHeight !== '0px') {
                    container.style.maxHeight = '0px';
                    if(arrowIcon) arrowIcon.style.transform = 'rotate(0deg)';
                } else {
                    container.style.maxHeight = container.scrollHeight + "px";
                    if(arrowIcon) arrowIcon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // --- File Drop Zone ---
        const dropZone = document.getElementById('drop-zone');
        const fileInput = document.getElementById('file-input');
        const dropZoneText = document.getElementById('drop-zone-text');

        // Allow clicking the zone to open file dialog
        dropZone.addEventListener('click', () => fileInput.click());
        
        dropZone.addEventListener('dragover', (e) => { 
            e.preventDefault(); 
            dropZone.style.borderColor = 'var(--accent-blue)';
            dropZone.style.backgroundColor = 'rgba(10, 132, 255, 0.1)';
        });
        dropZone.addEventListener('dragleave', () => {
            dropZone.style.borderColor = 'var(--border-color)';
            dropZone.style.backgroundColor = 'transparent';
        });
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.style.borderColor = 'var(--border-color)';
            dropZone.style.backgroundColor = 'transparent';
            const files = e.dataTransfer.files;
            if (files.length > 0) handleFile(files[0]);
        });
        fileInput.addEventListener('change', () => {
            if (fileInput.files.length > 0) handleFile(fileInput.files[0]);
        });

        function handleFile(file) {
            if (file && file.type.startsWith('audio/')) {
                dropZoneText.textContent = '已加载: ' + file.name;
                dropZone.style.borderColor = '#4CAF50'; // Green border on success
            } else {
                dropZoneText.textContent = '文件类型无效，请选择音频';
                dropZone.style.borderColor = '#F44336'; // Red border on error
            }
        }
        
        // --- Mock Song List ---
        const songListContainer = document.getElementById('song-list');
        const mockSongs = [
            '晴天 - 周杰伦.wav', '富士山下 - 陈奕迅.wav', '像我这样的人 - 毛不易.wav',
            '爱你 - 王心凌.wav', '稻香 - 周杰伦.wav'
        ];

        function createSongItem(songName) {
            const item = document.createElement('div');
            item.className = 'flex items-center justify-between p-2 rounded-lg';
            item.style.backgroundColor = 'var(--bg-dark-tertiary)';
            item.innerHTML = `
                <div class="flex items-center gap-3 truncate">
                    <span class="material-icons text-green-400">play_circle_filled</span>
                    <span class="truncate">${songName}</span>
                </div>
                <div class="flex items-center">
                    <button class="p-1 rounded-full hover:bg-gray-600"><span class="material-icons text-base">folder_open</span></button>
                    <button class="p-1 rounded-full hover:bg-gray-600"><span class="material-icons text-base">more_vert</span></button>
                </div>
            `;
            return item;
        }

        mockSongs.forEach(songName => {
            songListContainer.appendChild(createSongItem(songName));
        });

    </script>
</body>
</html>
