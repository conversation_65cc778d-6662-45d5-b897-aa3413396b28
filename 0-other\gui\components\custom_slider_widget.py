#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义滑块组件 - 带数值显示的滑块
"""

from PySide6.QtWidgets import QWidget, QHBoxLayout, QSlider, QLabel
from PySide6.QtCore import Qt, Signal


class CustomSliderWidget(QWidget):
    """自定义滑块组件 - 包含滑块和数值显示"""
    
    # 信号定义
    valueChanged = Signal(int)  # 值变化信号
    
    def __init__(self, minimum=0, maximum=100, value=0, step=1, 
                 orientation=Qt.Horizontal, suffix="", decimals=0, parent=None):
        super().__init__(parent)
        
        # 参数设置
        self.minimum = minimum
        self.maximum = maximum
        self.step = step
        self.suffix = suffix
        self.decimals = decimals
        self.scale_factor = 10 ** decimals  # 用于小数处理
        
        self.init_ui(orientation)
        self.set_value(value)
        
    def init_ui(self, orientation):
        """初始化UI"""
        # 创建布局
        if orientation == Qt.Horizontal:
            layout = QHBoxLayout(self)
        else:
            from PySide6.QtWidgets import QVBoxLayout
            layout = QVBoxLayout(self)
            
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 创建滑块
        self.slider = QSlider(orientation)
        self.slider.setMinimum(int(self.minimum * self.scale_factor))
        self.slider.setMaximum(int(self.maximum * self.scale_factor))
        self.slider.setSingleStep(int(self.step * self.scale_factor))
        
        # 创建数值标签
        self.value_label = QLabel()
        self.value_label.setProperty("class", "secondary")
        self.value_label.setMinimumWidth(60)
        self.value_label.setAlignment(Qt.AlignCenter)
        
        # 添加到布局
        if orientation == Qt.Horizontal:
            layout.addWidget(self.slider, 1)  # 滑块占据大部分空间
            layout.addWidget(self.value_label, 0)  # 标签固定宽度
        else:
            layout.addWidget(self.value_label, 0)
            layout.addWidget(self.slider, 1)
        
        # 连接信号
        self.slider.valueChanged.connect(self.on_slider_value_changed)
        
    def on_slider_value_changed(self, value):
        """滑块值变化处理"""
        real_value = value / self.scale_factor
        self.update_value_display(real_value)
        self.valueChanged.emit(value)
        
    def update_value_display(self, value):
        """更新数值显示"""
        if self.decimals == 0:
            text = f"{int(value)}{self.suffix}"
        else:
            text = f"{value:.{self.decimals}f}{self.suffix}"
        self.value_label.setText(text)
        
    def set_value(self, value):
        """设置滑块值"""
        scaled_value = int(value * self.scale_factor)
        self.slider.setValue(scaled_value)
        self.update_value_display(value)
        
    def get_value(self):
        """获取当前值"""
        return self.slider.value() / self.scale_factor
        
    def set_range(self, minimum, maximum):
        """设置范围"""
        self.minimum = minimum
        self.maximum = maximum
        self.slider.setMinimum(int(minimum * self.scale_factor))
        self.slider.setMaximum(int(maximum * self.scale_factor))
        
    def set_step(self, step):
        """设置步长"""
        self.step = step
        self.slider.setSingleStep(int(step * self.scale_factor))


class PitchSliderWidget(CustomSliderWidget):
    """音高滑块组件 - 专用于音高调节"""
    
    def __init__(self, parent=None):
        super().__init__(
            minimum=-12, 
            maximum=12, 
            value=0, 
            step=1,
            suffix="",
            decimals=0,
            parent=parent
        )
        
    def update_value_display(self, value):
        """更新音高显示 - 显示正负号"""
        if value > 0:
            text = f"+{int(value)}"
        else:
            text = f"{int(value)}"
        self.value_label.setText(text)


class ReverbSliderWidget(CustomSliderWidget):
    """混响滑块组件 - 专用于混响参数"""
    
    def __init__(self, minimum=0.0, maximum=1.0, value=0.5, parent=None):
        super().__init__(
            minimum=minimum,
            maximum=maximum,
            value=value,
            step=0.01,
            suffix="",
            decimals=2,
            parent=parent
        )


class DelaySliderWidget(CustomSliderWidget):
    """延迟滑块组件 - 专用于延迟参数"""
    
    def __init__(self, parent=None):
        super().__init__(
            minimum=0.0,
            maximum=2.0,
            value=0.0,
            step=0.01,
            suffix="s",
            decimals=2,
            parent=parent
        )


class CompressorSliderWidget(CustomSliderWidget):
    """压缩器滑块组件 - 专用于压缩器参数"""
    
    def __init__(self, minimum=-60, maximum=0, value=-20, suffix="dB", parent=None):
        super().__init__(
            minimum=minimum,
            maximum=maximum,
            value=value,
            step=1,
            suffix=suffix,
            decimals=0,
            parent=parent
        )
