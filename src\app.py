"""
主应用窗口模块 - 定义应用主窗口和基本框架结构
"""

import sys
from PySide6.QtCore import Qt, QSize
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, 
    QHBoxLayout, QTabBar, QStackedWidget, QLabel, QPushButton
)

from src.utils.style import APP_STYLE, TAB_BAR_STYLE, COLORS
from src.components.icons import IconButton, create_svg_pixmap, SVG_ICONS
from src.pages.song_production_page import SongProductionPage
from src.pages.api_management_page import ApiManagementPage


class MainWindow(QMainWindow):
    """主应用窗口类"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("DDSP歌声合成")
        
        # 设置窗口尺寸与位置
        self.resize(1000, 720)  # 设置初始大小而不是固定大小
        self.setMinimumSize(800, 600)  # 设置最小尺寸限制
        
        # 应用全局样式
        self._apply_style()
        
        # 设置主窗口UI
        self._setup_ui()
        
    def _apply_style(self):
        """应用样式表"""
        self.setStyleSheet(APP_STYLE)
        
    def _setup_ui(self):
        """设置主窗口UI"""
        # 创建中央部件
        self.central_widget = QWidget()
        self.central_widget.setObjectName("centralWidget")
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局
        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建顶部栏
        self._setup_top_bar()
        
        # 创建内容区
        self._setup_content_area()
        
    def _setup_top_bar(self):
        """设置顶部栏"""
        # 顶部栏容器
        self.top_bar = QWidget()
        self.top_bar.setObjectName("topBar")
        top_bar_layout = QHBoxLayout(self.top_bar)
        top_bar_layout.setContentsMargins(20, 0, 20, 0)
        
        # 创建标签页
        self.tab_bar = QTabBar()
        self.tab_bar.setObjectName("tabBar")
        self.tab_bar.setStyleSheet(TAB_BAR_STYLE)
        self.tab_bar.setExpanding(False)
        self.tab_bar.setDrawBase(False)
        
        # 添加标签页
        self.song_tab_index = self.tab_bar.addTab("歌曲制作")
        self.api_tab_index = self.tab_bar.addTab("API管理")
        
        # 设置标签页图标
        self.music_icon_active = create_svg_pixmap(SVG_ICONS["music"], 20, COLORS["accent_blue"])
        self.music_icon_inactive = create_svg_pixmap(SVG_ICONS["music"], 20, COLORS["text_secondary"])
        self.api_icon_active = create_svg_pixmap(SVG_ICONS["api"], 20, COLORS["accent_blue"])
        self.api_icon_inactive = create_svg_pixmap(SVG_ICONS["api"], 20, COLORS["text_secondary"])
        
        # 设置默认选中标签页
        self.tab_bar.setCurrentIndex(self.song_tab_index)
        
        # 手动更新第一次的标签颜色
        self._update_tab_icons(self.song_tab_index)
        
        # 连接标签页切换事件
        self.tab_bar.currentChanged.connect(self._on_tab_changed)
        
        top_bar_layout.addWidget(self.tab_bar)
        
        # 添加到主布局
        self.main_layout.addWidget(self.top_bar)
        
    def _setup_content_area(self):
        """设置内容区域"""
        # 创建内容区堆叠部件
        self.content_stack = QStackedWidget()
        
        # 创建歌曲制作页
        self.song_page = SongProductionPage()
        
        # 创建API管理页
        self.api_page = ApiManagementPage()
        
        # 连接API的启动和停止信号
        self.api_page.apiStarted.connect(self._on_api_started)
        self.api_page.apiStopped.connect(self._on_api_stopped)
        
        # 将页面添加到堆叠部件
        self.content_stack.addWidget(self.song_page)
        self.content_stack.addWidget(self.api_page)
        
        # 设置默认显示的页面
        self.content_stack.setCurrentIndex(0)
        
        # 添加到主布局
        self.main_layout.addWidget(self.content_stack)
        
    def _on_tab_changed(self, index):
        """标签页切换事件处理"""
        # 切换内容页面
        self.content_stack.setCurrentIndex(index)
        
        # 更新标签页图标和样式
        self._update_tab_icons(index)
    
    def _update_tab_icons(self, active_index):
        """根据活动标签更新图标状态"""
        # 根据当前激活的标签页索引设置图标和文本颜色
        if active_index == self.song_tab_index:
            # 音乐标签激活，API标签未激活
            self.tab_bar.setTabTextColor(self.song_tab_index, Qt.GlobalColor.white)
            self.tab_bar.setTabTextColor(self.api_tab_index, COLORS["text_secondary"])
        else:
            # API标签激活，音乐标签未激活  
            self.tab_bar.setTabTextColor(self.song_tab_index, COLORS["text_secondary"])
            self.tab_bar.setTabTextColor(self.api_tab_index, Qt.GlobalColor.white)
    
    def _on_api_started(self):
        """API启动时的处理"""
        # 更新歌曲制作页面状态，告知API已启动
        self.song_page.on_api_status_changed(True)
    
    def _on_api_stopped(self):
        """API停止时的处理"""
        # 更新歌曲制作页面状态，告知API已停止
        self.song_page.on_api_status_changed(False)
        
    def closeEvent(self, event):
        """窗口关闭事件处理"""
        # 确保关闭窗口时停止API服务
        if hasattr(self, 'api_page'):
            self.api_page._stop_api()
        event.accept()


# 如果直接运行此脚本，则创建应用实例并显示主窗口
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec()) 