"""
API管理页面模块 - 提供API配置和控制功能
"""

from PySide6.QtCore import Qt, Signal, QProcess
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, 
    QFrame, QLabel, QLineEdit, QPushButton,
    QCheckBox, QPlainTextEdit, QFileDialog,
    QSizePolicy
)

from src.utils.style import COLORS


class ApiManagementPage(QWidget):
    """API管理页面类"""
    
    # 当API启动或停止时发出的信号
    apiStarted = Signal()
    apiStopped = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # API服务进程
        self.api_process = None
        
        # 设置尺寸策略，使组件能够随窗口大小变化
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        self._setup_ui()
        
    def _setup_ui(self):
        """设置页面UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建API配置面板
        self._setup_config_panel()
        main_layout.addWidget(self.config_panel)
        
        # 创建控制台输出面板
        self._setup_console_panel()
        main_layout.addWidget(self.console_panel, 1)  # 使控制台面板可以伸展
        
    def _setup_config_panel(self):
        """设置API配置面板"""
        # 配置面板容器
        self.config_panel = QFrame()
        self.config_panel.setProperty("frameType", "panel")
        self.config_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        
        # 配置面板布局
        config_layout = QVBoxLayout(self.config_panel)
        config_layout.setContentsMargins(16, 16, 16, 16)
        config_layout.setSpacing(16)
        
        # 面板标题
        title = QLabel("API 配置")
        title.setProperty("labelType", "title")
        config_layout.addWidget(title)
        
        # API URL配置
        url_layout = QVBoxLayout()
        url_layout.setSpacing(8)
        
        url_label = QLabel("API URL")
        url_label.setProperty("labelType", "secondary")
        self.url_input = QLineEdit("http://127.0.0.1:9880")
        self.url_input.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        url_layout.addWidget(url_label)
        url_layout.addWidget(self.url_input)
        config_layout.addLayout(url_layout)
        
        # Python环境路径
        python_layout = QVBoxLayout()
        python_layout.setSpacing(8)
        
        python_label = QLabel("Python 环境路径")
        python_label.setProperty("labelType", "secondary")
        
        python_input_layout = QHBoxLayout()
        python_input_layout.setSpacing(8)
        
        self.python_input = QLineEdit()
        self.python_input.setPlaceholderText("例如: C:\\path\\to\\venv\\python.exe")
        self.python_input.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        self.browse_button = QPushButton("浏览...")
        self.browse_button.setProperty("buttonType", "secondary")
        self.browse_button.clicked.connect(self._browse_python_path)
        
        python_input_layout.addWidget(self.python_input)
        python_input_layout.addWidget(self.browse_button)
        
        python_layout.addWidget(python_label)
        python_layout.addLayout(python_input_layout)
        config_layout.addLayout(python_layout)
        
        # 复选框选项
        options_layout = QHBoxLayout()
        options_layout.setSpacing(16)
        
        self.auto_start_checkbox = QCheckBox("随软件启动API")
        self.auto_start_checkbox.setChecked(True)
        options_layout.addWidget(self.auto_start_checkbox)
        
        self.cloud_api_checkbox = QCheckBox("云端API")
        options_layout.addWidget(self.cloud_api_checkbox)
        
        options_layout.addStretch()
        config_layout.addLayout(options_layout)
        
    def _setup_console_panel(self):
        """设置控制台输出面板"""
        # 控制台面板容器
        self.console_panel = QFrame()
        self.console_panel.setProperty("frameType", "panel")
        self.console_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 控制台面板布局
        console_layout = QVBoxLayout(self.console_panel)
        console_layout.setContentsMargins(16, 16, 16, 16)
        console_layout.setSpacing(16)
        
        # 控制台标题
        console_title = QLabel("控制台输出")
        console_title.setProperty("labelType", "title")
        console_layout.addWidget(console_title)
        
        # 控制台输出文本区域
        self.console_output = QPlainTextEdit()
        self.console_output.setReadOnly(True)
        self.console_output.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.console_output.setStyleSheet("""
            QPlainTextEdit {
                background-color: black;
                color: #AAAAAA;
                font-family: Consolas, monospace;
                font-size: 13px;
                padding: 8px;
                border-radius: 8px;
            }
        """)
        self.console_output.appendPlainText("> API服务未启动...")
        console_layout.addWidget(self.console_output)
        
        # 控制按钮布局
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(16)
        
        # 启动API按钮
        self.start_button = QPushButton("启动 API")
        self.start_button.setProperty("buttonType", "primary")
        self.start_button.clicked.connect(self._start_api)
        buttons_layout.addWidget(self.start_button)
        
        # 停止API按钮
        self.stop_button = QPushButton("停止 API")
        self.stop_button.setProperty("buttonType", "danger")
        self.stop_button.clicked.connect(self._stop_api)
        self.stop_button.setEnabled(False)  # 初始状态下禁用
        buttons_layout.addWidget(self.stop_button)
        
        console_layout.addLayout(buttons_layout)
        
    def _browse_python_path(self):
        """打开文件对话框浏览Python可执行文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "选择Python可执行文件", 
            "", 
            "Python可执行文件 (*.exe);;所有文件 (*)"
        )
        
        if file_path:
            self.python_input.setText(file_path)
            
    def _start_api(self):
        """启动API服务"""
        # 如果API已经在运行，先停止
        if self.api_process and self.api_process.state() == QProcess.Running:
            self._stop_api()
            
        # 创建新进程
        self.api_process = QProcess()
        self.api_process.setProcessChannelMode(QProcess.MergedChannels)
        self.api_process.readyReadStandardOutput.connect(self._handle_api_output)
        self.api_process.finished.connect(self._handle_api_finished)
        
        # 获取Python路径和API URL
        python_path = self.python_input.text().strip() or "python"  # 如果未设置，使用系统默认Python
        api_url = self.url_input.text().strip()
        
        # 准备启动命令（这里是模拟，实际应用中需要替换为真实API启动命令）
        self.console_output.appendPlainText(f"> 正在启动API服务 ({api_url})...")
        
        # 模拟启动命令，应替换为实际命令
        command = f"{python_path} -c \"import time; print('API服务正在启动...'); time.sleep(1); print('API服务已启动，监听地址: {api_url}');\""
        
        # 启动进程
        self.api_process.start(command)
        
        # 更新按钮状态
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        
        # 发出API已启动信号
        self.apiStarted.emit()
            
    def _stop_api(self):
        """停止API服务"""
        if self.api_process and self.api_process.state() == QProcess.Running:
            self.console_output.appendPlainText("> 正在停止API服务...")
            
            # 尝试正常终止进程
            self.api_process.terminate()
            
            # 等待进程终止，最多等待3秒
            if not self.api_process.waitForFinished(3000):
                # 如果超时，强制终止
                self.api_process.kill()
                self.console_output.appendPlainText("> API服务已强制终止")
            
            # 更新按钮状态
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            
            # 发出API已停止信号
            self.apiStopped.emit()
        
    def _handle_api_output(self):
        """处理API进程的输出"""
        output = self.api_process.readAllStandardOutput()
        text = output.data().decode('utf-8', errors='ignore').strip()
        self.console_output.appendPlainText(f"> {text}")
        
    def _handle_api_finished(self, exit_code, exit_status):
        """处理API进程结束事件"""
        if exit_status == QProcess.NormalExit:
            self.console_output.appendPlainText(f"> API服务已退出，退出码: {exit_code}")
        else:
            self.console_output.appendPlainText("> API服务异常终止")
            
        # 更新按钮状态
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        # 发出API已停止信号
        self.apiStopped.emit() 