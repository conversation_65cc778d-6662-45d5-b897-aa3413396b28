#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频播放器组件 - 支持播放控制、进度条和时间显示
"""

import os
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
    QSlider, QFrame, QSizePolicy
)
from PySide6.QtCore import Qt, Signal, QUrl, QTimer
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtGui import QFont


class AudioPlayerWidget(QFrame):
    """音频播放器组件"""
    
    # 信号定义
    playback_started = Signal()
    playback_paused = Signal()
    playback_stopped = Signal()
    position_changed = Signal(int)  # 播放位置变化
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化媒体播放器
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setAudioOutput(self.audio_output)
        
        # 状态变量
        self.current_file = None
        self.is_seeking = False  # 是否正在拖拽进度条
        
        self.init_ui()
        self.setup_connections()
        
        # 初始状态为隐藏
        self.setVisible(False)
        
    def init_ui(self):
        """初始化UI"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMaximumHeight(120)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(8)
        
        # 文件名标签
        self.file_name_label = QLabel("未选择文件")
        self.file_name_label.setProperty("class", "secondary")
        font = QFont()
        font.setBold(True)
        self.file_name_label.setFont(font)
        layout.addWidget(self.file_name_label)
        
        # 控制按钮和进度条布局
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(12)
        
        # 播放/暂停按钮
        self.play_pause_btn = QPushButton("▶")
        self.play_pause_btn.setFixedSize(40, 40)
        self.play_pause_btn.setStyleSheet("""
            QPushButton {
                background-color: #1E88E5;
                border: none;
                border-radius: 20px;
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #42A5F5;
            }
            QPushButton:pressed {
                background-color: #1976D2;
            }
        """)
        controls_layout.addWidget(self.play_pause_btn)
        
        # 进度条
        self.progress_slider = QSlider(Qt.Horizontal)
        self.progress_slider.setMinimum(0)
        self.progress_slider.setMaximum(100)
        self.progress_slider.setValue(0)
        self.progress_slider.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        controls_layout.addWidget(self.progress_slider)
        
        # 时间显示
        self.time_label = QLabel("0:00 / 0:00")
        self.time_label.setProperty("class", "secondary")
        self.time_label.setMinimumWidth(80)
        self.time_label.setAlignment(Qt.AlignCenter)
        controls_layout.addWidget(self.time_label)
        
        layout.addLayout(controls_layout)
        
    def setup_connections(self):
        """设置信号连接"""
        # 播放器信号
        self.media_player.positionChanged.connect(self.on_position_changed)
        self.media_player.durationChanged.connect(self.on_duration_changed)
        self.media_player.playbackStateChanged.connect(self.on_playback_state_changed)
        
        # 控件信号
        self.play_pause_btn.clicked.connect(self.toggle_playback)
        self.progress_slider.sliderPressed.connect(self.on_slider_pressed)
        self.progress_slider.sliderReleased.connect(self.on_slider_released)
        self.progress_slider.valueChanged.connect(self.on_slider_value_changed)
        
    def load_file(self, file_path):
        """加载音频文件"""
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return False
            
        try:
            self.current_file = file_path
            file_name = os.path.basename(file_path)
            self.file_name_label.setText(file_name)
            
            # 加载媒体文件
            media_url = QUrl.fromLocalFile(file_path)
            self.media_player.setSource(media_url)
            
            # 显示播放器
            self.setVisible(True)
            
            print(f"✓ 音频文件已加载: {file_name}")
            return True
            
        except Exception as e:
            print(f"加载音频文件失败: {e}")
            return False
    
    def toggle_playback(self):
        """切换播放/暂停状态"""
        if self.media_player.playbackState() == QMediaPlayer.PlayingState:
            self.media_player.pause()
        else:
            self.media_player.play()
    
    def stop_playback(self):
        """停止播放"""
        self.media_player.stop()
    
    def on_position_changed(self, position):
        """播放位置变化"""
        if not self.is_seeking:
            duration = self.media_player.duration()
            if duration > 0:
                progress = int((position / duration) * 100)
                self.progress_slider.setValue(progress)
        
        # 更新时间显示
        self.update_time_display(position, self.media_player.duration())
        self.position_changed.emit(position)
    
    def on_duration_changed(self, duration):
        """音频时长变化"""
        self.update_time_display(self.media_player.position(), duration)
    
    def on_playback_state_changed(self, state):
        """播放状态变化"""
        if state == QMediaPlayer.PlayingState:
            self.play_pause_btn.setText("⏸")
            self.playback_started.emit()
        elif state == QMediaPlayer.PausedState:
            self.play_pause_btn.setText("▶")
            self.playback_paused.emit()
        elif state == QMediaPlayer.StoppedState:
            self.play_pause_btn.setText("▶")
            self.progress_slider.setValue(0)
            self.playback_stopped.emit()
    
    def on_slider_pressed(self):
        """进度条按下"""
        self.is_seeking = True
    
    def on_slider_released(self):
        """进度条释放"""
        self.is_seeking = False
        # 设置播放位置
        duration = self.media_player.duration()
        if duration > 0:
            position = int((self.progress_slider.value() / 100) * duration)
            self.media_player.setPosition(position)
    
    def on_slider_value_changed(self, value):
        """进度条值变化"""
        if self.is_seeking:
            # 实时更新时间显示
            duration = self.media_player.duration()
            if duration > 0:
                position = int((value / 100) * duration)
                self.update_time_display(position, duration)
    
    def update_time_display(self, position, duration):
        """更新时间显示"""
        current_time = self.format_time(position)
        total_time = self.format_time(duration)
        self.time_label.setText(f"{current_time} / {total_time}")
    
    def format_time(self, milliseconds):
        """格式化时间显示"""
        if milliseconds <= 0:
            return "0:00"
            
        seconds = milliseconds // 1000
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes}:{seconds:02d}"
    
    def clear(self):
        """清除播放器"""
        self.media_player.stop()
        self.media_player.setSource(QUrl())
        self.current_file = None
        self.file_name_label.setText("未选择文件")
        self.time_label.setText("0:00 / 0:00")
        self.progress_slider.setValue(0)
        self.setVisible(False)
