"""
歌曲制作页面模块 - 实现歌曲制作功能的主页面
"""

from PySide6.QtCore import Qt
from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, 
    QFrame, QScrollArea, QLabel, 
    QSplitter, QSizePolicy
)

from src.utils.style import COLORS
from src.components.panels.left_panel import LeftPanel
from src.components.panels.model_pitch_panel import ModelPitchPanel
from src.components.panels.reverb_panel import ReverbPanel
from src.components.panels.advanced_settings_panel import AdvancedSettingsPanel


class SongProductionPage(QWidget):
    """歌曲制作页面"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.api_active = False  # API服务状态，默认为未启动
        self._setup_ui()
        
        # 设置尺寸策略，使组件能够随窗口大小变化
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

    def _setup_ui(self):
        """设置页面UI"""
        # 主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # 创建左侧和右侧的面板
        self._setup_left_panel()
        self._setup_right_panel()
        
        # 创建分割器
        self.splitter = QSplitter(Qt.Horizontal)
        self.splitter.addWidget(self.left_panel)
        self.splitter.addWidget(self.right_panel)
        
        # 设置分割器初始比例
        self.splitter.setSizes([300, 700])  # 初始比例约为1:2
        
        # 设置分割器的尺寸策略
        self.splitter.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 添加到主布局
        main_layout.addWidget(self.splitter)
        
        # 初始设置API状态
        self._update_ui_for_api_status()
        
        # 连接左侧面板歌曲列表展开信号
        self.left_panel.songs_panel.panelToggled.connect(self._on_songs_panel_toggled)
        
        # 连接右侧面板折叠互斥信号
        self.reverb_panel.panelToggled.connect(self._on_panel_toggled)
        self.advanced_settings_panel.panelToggled.connect(self._on_panel_toggled)

    def _setup_left_panel(self):
        """设置左侧面板"""
        self.left_panel = LeftPanel()
        
        # 设置左侧面板的尺寸策略
        self.left_panel.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)
        self.left_panel.setMinimumWidth(250)  # 设置最小宽度，确保内容可见

    def _setup_right_panel(self):
        """设置右侧面板"""
        # 创建右侧面板容器
        self.right_panel = QWidget()
        
        # 设置右侧面板的尺寸策略
        self.right_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 创建右侧面板布局
        right_layout = QVBoxLayout(self.right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(20)
        
        # 添加模型与音高面板
        self.model_pitch_panel = ModelPitchPanel()
        self.model_pitch_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        right_layout.addWidget(self.model_pitch_panel)
        
        # 创建一个滚动区域来容纳其余面板
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 创建滚动区域的内容容器
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(20)
        
        # 添加可折叠面板
        self.reverb_panel = ReverbPanel()
        self.reverb_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        scroll_layout.addWidget(self.reverb_panel)
        
        # 添加高级参数设置面板（合并了推理参数和高级音频参数）
        self.advanced_settings_panel = AdvancedSettingsPanel()
        self.advanced_settings_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        scroll_layout.addWidget(self.advanced_settings_panel)
        
        # 添加底部弹性空间
        scroll_layout.addStretch()
        
        # 设置滚动区域的内容
        scroll_area.setWidget(scroll_content)
        
        # 将滚动区域添加到右侧布局
        right_layout.addWidget(scroll_area)
    
    def _on_panel_toggled(self, panel_title, is_expanded):
        """处理右侧面板展开/折叠状态变化，实现互斥展开"""
        if not is_expanded:
            # 如果是折叠，则不需要处理
            return
            
        # 如果是混响面板展开，则关闭高级参数设置面板
        if panel_title == "混响与和声" and is_expanded and self.advanced_settings_panel.is_expanded():
            self.advanced_settings_panel.set_expanded(False)
            
        # 如果是高级参数设置面板展开，则关闭混响面板
        elif panel_title == "高级参数设置" and is_expanded and self.reverb_panel.is_expanded():
            self.reverb_panel.set_expanded(False)
            
    def _on_songs_panel_toggled(self, panel_title, is_expanded):
        """处理歌曲列表面板展开/折叠状态变化，实现歌曲列表展开时隐藏上方组件"""
        if panel_title == "歌曲列表":
            # 当歌曲列表展开时，隐藏上传组件，否则显示
            self.left_panel.upload_stack.setVisible(not is_expanded)
            
    def on_api_status_changed(self, is_active):
        """
        处理API状态变化
        
        Args:
            is_active (bool): API是否处于活动状态
        """
        self.api_active = is_active
        self._update_ui_for_api_status()
    
    def _update_ui_for_api_status(self):
        """根据API状态更新UI元素"""
        # 更新左侧面板的API状态显示
        self.left_panel.update_api_status(self.api_active)