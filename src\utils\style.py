"""
样式工具模块 - 定义应用的全局样式表和颜色主题
此模块将HTML/CSS样式表转换为Qt兼容的QSS样式表
"""

# 色彩方案定义 - 来源于HTML的:root变量
COLORS = {
    "bg_dark_primary": "#1C1C1E",      # 应用程序背景，非常深的灰色
    "bg_dark_secondary": "#2C2C2E",   # 面板/卡片背景
    "bg_dark_tertiary": "#3A3A3C",    # 输入字段，悬停状态
    "bg_light_gray": "#5A5A5C",       # 浅灰色背景（用于输入框）
    "accent_blue": "#0A84FF",         # 蓝色主要操作颜色
    "accent_blue_hover": "#3DA0FF",   # 悬停时的较浅蓝色
    "accent_blue_light": "#4DA6FF",   # 淡蓝色（用于边框）
    "text_primary": "#F2F2F7",       # 主要文本颜色，接近白色
    "text_secondary": "#8A8A8E",     # 次要/标签文本，灰色
    "border_color": "#444446",       # 微妙的边框颜色
}

# 通用应用程序样式表
APP_STYLE = f"""
/* 全局应用样式 */
QWidget {{
    background-color: {COLORS["bg_dark_primary"]};
    color: {COLORS["text_primary"]};
    font-family: -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}}

/* 主应用窗口 */
MainWindow {{
    background-color: #101012;
    border-radius: 12px;
}}

/* 标签 */
QLabel {{
    color: {COLORS["text_primary"]};
    background-color: transparent;
}}

QLabel[labelType="secondary"] {{
    color: {COLORS["text_secondary"]};
    font-size: 13px;
    background-color: transparent;
}}

/* 按钮通用样式 */
QPushButton {{
    border-radius: 8px;
    padding: 12px 20px;
    font-weight: 600;
    color: white;
}}

QPushButton[buttonType="primary"] {{
    background-color: {COLORS["accent_blue"]};
}}

QPushButton[buttonType="primary"]:hover {{
    background-color: {COLORS["accent_blue_hover"]};
}}

QPushButton[buttonType="secondary"] {{
    background-color: {COLORS["bg_dark_tertiary"]};
}}

QPushButton[buttonType="secondary"]:hover {{
    background-color: #4F4F52;
}}

QPushButton[buttonType="danger"] {{
    background-color: #E53E3E;
}}

QPushButton[buttonType="danger"]:hover {{
    background-color: #FC8181;
}}

/* 文本框 */
QLineEdit, QTextEdit, QPlainTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {{
    background-color: {COLORS["bg_light_gray"]};
    color: {COLORS["text_primary"]};
    border: 1px solid {COLORS["accent_blue_light"]};
    border-radius: 8px;
    padding: 10px 12px;
}}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, QComboBox:focus {{
    border-color: {COLORS["accent_blue"]};
}}

/* 下拉框 */
QComboBox {{
    padding-right: 20px;
}}

QComboBox::drop-down {{
    subcontrol-origin: padding;
    subcontrol-position: right;
    width: 20px;
    border-left-width: 0px;
}}

QComboBox::down-arrow {{
    image: url(src/resources/down_arrow.png);
}}

QComboBox QAbstractItemView {{
    background-color: {COLORS["bg_dark_tertiary"]};
    border: 1px solid {COLORS["border_color"]};
    selection-background-color: {COLORS["bg_dark_primary"]};
}}

/* 滑块 */
QSlider::groove:horizontal {{
    height: 6px;
    background: {COLORS["bg_dark_tertiary"]};
    border-radius: 3px;
}}

QSlider::handle:horizontal {{
    background: {COLORS["accent_blue"]};
    border: 3px solid {COLORS["bg_dark_tertiary"]};
    width: 18px;
    height: 18px;
    margin: -6px 0;
    border-radius: 9px;
}}

/* 面板样式 */
QFrame[frameType="panel"] {{
    background-color: {COLORS["bg_dark_secondary"]};
    border: 1px solid {COLORS["border_color"]};
    border-radius: 12px;
}}

/* 复选框 */
QCheckBox {{
    color: {COLORS["text_secondary"]};
    spacing: 10px;
    background-color: transparent;
}}

QCheckBox::indicator {{
    width: 18px;
    height: 18px;
    border-radius: 5px;
    border: 1px solid {COLORS["border_color"]};
    background-color: {COLORS["bg_dark_tertiary"]};
}}

QCheckBox::indicator:checked {{
    background-color: {COLORS["accent_blue"]};
    border-color: {COLORS["accent_blue"]};
    image: url(src/resources/checkmark.png);
}}

/* 标签页 */
QTabBar::tab {{
    background: transparent;
    color: {COLORS["text_secondary"]};
    padding: 14px 4px;
    margin: 0px 12px;
    border-bottom: 3px solid transparent;
}}

QTabBar::tab:first {{
    margin-left: 0;
}}

QTabBar::tab:hover {{
    color: {COLORS["text_primary"]};
}}

QTabBar::tab:selected {{
    color: {COLORS["accent_blue"]};
    font-weight: 600;
    border-bottom-color: {COLORS["accent_blue"]};
}}

/* 滚动区域 */
QScrollArea, QScrollArea * {{
    background-color: transparent;
    border: none;
}}

QScrollBar:vertical {{
    background: transparent;
    width: 8px;
    margin: 0px;
}}

QScrollBar::handle:vertical {{
    background: #555;
    border-radius: 4px;
    min-height: 20px;
}}

QScrollBar::handle:vertical:hover {{
    background: #777;
}}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
    height: 0px;
}}

QScrollBar:horizontal {{
    background: transparent;
    height: 8px;
    margin: 0px;
}}

QScrollBar::handle:horizontal {{
    background: #555;
    border-radius: 4px;
    min-width: 20px;
}}

QScrollBar::handle:horizontal:hover {{
    background: #777;
}}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
    width: 0px;
}}
"""

# 专门为主窗口设计的QSS样式表
MAIN_WINDOW_STYLE = """
#centralWidget {
    background-color: #101012;
    border-radius: 12px;
}
"""

# 定义自定义组件的特定样式
TAB_BAR_STYLE = f"""
QWidget#tabBar {{
    background-color: transparent;
    border-bottom: 1px solid {COLORS["border_color"]};
}}
"""

# 标题标签样式（用于面板标题等）
TITLE_LABEL_STYLE = f"""
QLabel[labelType="title"] {{
    color: {COLORS["text_primary"]};
    font-size: 16px;
    font-weight: 600;
    background-color: transparent;
}}
"""

# 文件拖放区样式
DROP_ZONE_STYLE = f"""
QFrame#dropZone {{
    border: 2px dashed {COLORS["border_color"]};
    border-radius: 8px;
    background-color: transparent;
}}

QFrame#dropZone:hover {{
    border-color: {COLORS["accent_blue"]};
    background-color: rgba(10, 132, 255, 0.1);
}}
"""

# 折叠面板标题按钮样式
COLLAPSIBLE_BUTTON_STYLE = f"""
QPushButton[buttonType="collapsible"] {{
    background-color: transparent;
    border: none;
    text-align: left;
    font-size: 16px;
    font-weight: 600;
    color: {COLORS["text_primary"]};
    padding: 0px;
}}
"""

# 圆形按钮样式
ROUND_BUTTON_STYLE = f"""
QPushButton[buttonType="round"] {{
    background-color: transparent;
    border-radius: 15px;
    padding: 5px;
}}

QPushButton[buttonType="round"]:hover {{
    background-color: #4F4F52;
}}
""" 